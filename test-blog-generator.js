// Simple test script for the blog generator API
const testBlogGenerator = async () => {
  try {
    console.log('🧪 Testing blog generator API...')
    
    const response = await fetch('http://localhost:3001/api/generate/blog-post', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        topic: 'The Future of Artificial Intelligence in 2025',
        wordCount: 800,
        tone: 'professional',
        language: 'en'
      })
    })

    const data = await response.json()
    
    if (data.success) {
      console.log('✅ Blog generation successful!')
      console.log('📄 Title:', data.article.title)
      console.log('📊 Word count:', data.article.metadata.wordCount)
      console.log('🔧 Generation method:', data.article.metadata.generationMethod)
      console.log('📝 Content preview:', data.article.content.substring(0, 200) + '...')
    } else {
      console.error('❌ Blog generation failed:', data.error)
    }
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test
testBlogGenerator()

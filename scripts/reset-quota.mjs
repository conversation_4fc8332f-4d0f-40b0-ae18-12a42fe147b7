#!/usr/bin/env node

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const QUOTA_TYPES = ['blog_posts', 'emails', 'social_media', 'youtube_scripts']

async function resetAllUserQuotas() {
  try {
    console.log('🔄 Starting quota reset for all users...')
    
    // Get all users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true
      }
    })
    
    console.log(`📊 Found ${users.length} users`)
    
    let totalResets = 0
    
    for (const user of users) {
      console.log(`\n👤 Resetting quotas for: ${user.name || user.email}`)
      
      for (const quotaType of QUOTA_TYPES) {
        try {
          // Reset the quota
          const result = await prisma.userQuota.updateMany({
            where: {
              userId: user.id,
              quotaType: quotaType
            },
            data: {
              used: 0,
              resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
            }
          })
          
          if (result.count > 0) {
            console.log(`  ✅ Reset ${quotaType}: usage set to 0`)
            totalResets++
          } else {
            console.log(`  ⏭️  No existing quota for ${quotaType}`)
          }
        } catch (error) {
          console.error(`  ❌ Failed to reset ${quotaType}:`, error.message)
        }
      }
    }
    
    console.log(`\n🎉 Quota reset completed!`)
    console.log(`📈 Total quotas reset: ${totalResets}`)
    console.log(`👥 Users processed: ${users.length}`)
    
    // Optional: Also clear usage history if needed
    const clearHistory = process.argv.includes('--clear-history')
    if (clearHistory) {
      console.log('\n🗑️  Clearing usage history...')
      const deletedHistory = await prisma.usageHistory.deleteMany({})
      console.log(`✅ Cleared ${deletedHistory.count} usage history records`)
    }
    
  } catch (error) {
    console.error('❌ Error during quota reset:', error)
    throw error
  }
}

async function resetSpecificUser(userId) {
  try {
    console.log(`🔄 Resetting quotas for user: ${userId}`)
    
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true
      }
    })
    
    if (!user) {
      console.error(`❌ User not found: ${userId}`)
      return
    }
    
    console.log(`👤 User: ${user.name || user.email}`)
    let resetCount = 0
    
    for (const quotaType of QUOTA_TYPES) {
      try {
        const result = await prisma.userQuota.updateMany({
          where: {
            userId: user.id,
            quotaType: quotaType
          },
          data: {
            used: 0,
            resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
          }
        })
        
        if (result.count > 0) {
          console.log(`  ✅ Reset ${quotaType}: usage set to 0`)
          resetCount++
        } else {
          console.log(`  ⏭️  No existing quota for ${quotaType}`)
        }
      } catch (error) {
        console.error(`  ❌ Failed to reset ${quotaType}:`, error.message)
      }
    }
    
    console.log(`\n🎉 Reset completed! ${resetCount} quotas reset for user.`)
    
  } catch (error) {
    console.error('❌ Error during user quota reset:', error)
    throw error
  }
}

async function showCurrentQuotas(userId = null) {
  try {
    const whereClause = userId ? { id: userId } : {}
    
    const users = await prisma.user.findMany({
      where: whereClause,
      include: {
        quotas: true,
        subscription: true
      }
    })
    
    console.log('📊 Current Quota Status:')
    console.log('=' .repeat(80))
    
    for (const user of users) {
      console.log(`\n👤 ${user.name || user.email} (${user.subscription?.plan || 'free'})`)
      
      if (user.quotas.length === 0) {
        console.log('  No quotas found')
        continue
      }
      
      for (const quota of user.quotas) {
        console.log(`  ${quota.quotaType}: ${quota.used}/${quota.totalLimit} (resets: ${quota.resetDate.toISOString().split('T')[0]})`)
      }
    }
  } catch (error) {
    console.error('❌ Error showing quotas:', error)
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2)
  
  try {
    if (args.includes('--help') || args.includes('-h')) {
      console.log(`
📋 Quota Reset Script Usage:

  node scripts/reset-quota.mjs [options]

Options:
  --all                Reset quotas for all users
  --user <userId>      Reset quotas for specific user
  --show [userId]      Show current quota status (all users or specific user)
  --clear-history      Also clear usage history (use with --all)
  --help, -h           Show this help message

Examples:
  node scripts/reset-quota.mjs --all
  node scripts/reset-quota.mjs --user clr123abc456
  node scripts/reset-quota.mjs --show
  node scripts/reset-quota.mjs --show clr123abc456
  node scripts/reset-quota.mjs --all --clear-history
`)
      return
    }
    
    if (args.includes('--show')) {
      const userIdIndex = args.indexOf('--show') + 1
      const userId = args[userIdIndex] && !args[userIdIndex].startsWith('--') ? args[userIdIndex] : null
      await showCurrentQuotas(userId)
    } else if (args.includes('--all')) {
      await resetAllUserQuotas()
    } else if (args.includes('--user')) {
      const userIdIndex = args.indexOf('--user') + 1
      const userId = args[userIdIndex]
      if (!userId) {
        console.error('❌ Please provide a user ID with --user option')
        process.exit(1)
      }
      await resetSpecificUser(userId)
    } else {
      console.log('🤖 Quota Reset Tool')
      console.log('Use --help for usage information')
      await showCurrentQuotas()
    }
    
  } catch (error) {
    console.error('❌ Script execution failed:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
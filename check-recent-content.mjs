import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkRecentContent() {
  try {
    console.log('🔍 Checking recent blog content for table issues...');
    
    // Get the 5 most recent blog posts
    const recentContent = await prisma.content.findMany({
      where: {
        type: 'blog'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5,
      select: {
        id: true,
        title: true,
        content: true,
        wordCount: true,
        createdAt: true
      }
    });

    if (recentContent.length === 0) {
      console.log('❌ No blog content found in database');
      return;
    }

    console.log(`\n📊 Found ${recentContent.length} recent blog posts:`);
    
    recentContent.forEach((post, index) => {
      console.log(`\n--- Post ${index + 1} ---`);
      console.log(`ID: ${post.id}`);
      console.log(`Title: ${post.title}`);
      console.log(`Word Count: ${post.wordCount}`);
      console.log(`Created: ${post.createdAt.toISOString()}`);
      
      // Check for table content
      const hasTableSyntax = post.content.includes('|') && post.content.includes('---');
      const tableRows = (post.content.match(/\|.*\|/g) || []).length;
      
      console.log(`Has tables: ${hasTableSyntax ? '✅' : '❌'}`);
      console.log(`Table rows: ${tableRows}`);
      
      if (hasTableSyntax) {
        console.log('\n📋 Table content found:');
        const tableLines = post.content.split('\n').filter(line => line.trim().includes('|') && line.trim().length > 0);
        tableLines.slice(0, 10).forEach((line, i) => {
          console.log(`  ${i + 1}: ${line.trim()}`);
        });
        if (tableLines.length > 10) {
          console.log(`  ... and ${tableLines.length - 10} more table lines`);
        }
      }
      
      // Show first 200 characters of content
      console.log(`\n📝 Content preview:`);
      console.log(post.content.substring(0, 200) + '...');
    });

  } catch (error) {
    console.error('❌ Error checking content:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRecentContent();

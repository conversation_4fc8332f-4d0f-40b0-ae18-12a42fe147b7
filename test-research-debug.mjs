// Debug script to test research functionality directly
import { TavilySearchService } from './src/lib/search.js';

async function testResearch() {
  console.log('🔍 Testing research functionality...');
  
  try {
    const searchService = new TavilySearchService();
    console.log('✅ TavilySearchService initialized');
    
    const topic = 'Google Genie 3 AI breakthrough';
    console.log(`🚀 Testing search for: "${topic}"`);
    
    const searchResponse = await searchService.search(topic, 5, {
      searchDepth: 'advanced',
      prioritizeRecent: true,
      temporalFocus: 'current'
    });
    
    console.log('📊 Search response:', {
      hasResponse: !!searchResponse,
      hasItems: !!searchResponse?.items,
      itemCount: searchResponse?.items?.length || 0
    });
    
    if (searchResponse && searchResponse.items && searchResponse.items.length > 0) {
      console.log('✅ Search successful!');
      console.log('📄 First result:', {
        title: searchResponse.items[0].title,
        url: searchResponse.items[0].url,
        hasContent: !!searchResponse.items[0].content
      });
    } else {
      console.log('❌ No search results found');
    }
    
  } catch (error) {
    console.error('❌ Research test failed:', error);
  }
}

testResearch();

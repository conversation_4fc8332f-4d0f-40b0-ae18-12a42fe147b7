# Megatron Agent Documentation

## Overview

Megatron is a powerful YouTube video analyzer that extracts captions, analyzes content structure, and generates interactive cards for content creation. It's designed to handle both single-topic and multi-topic videos, making it perfect for tech news channels and educational content.

## Features

### Core Capabilities
- **YouTube Caption Extraction**: Advanced caption extraction from any YouTube video with multi-language support
- **Smart Analysis**: AI-powered topic identification and content structure analysis using Gemini 2.5 Flash
- **Topic Detection**: Automatically determines if a video covers single or multiple topics
- **Interactive Cards**: Generates beautiful, interactive cards for each identified topic
- **Content Generation**: Direct integration with Invincible for article generation and YouTube Scripts for script generation

### Technical Features
- Advanced YouTube URL validation
- Real-time caption extraction using multiple fallback methods
- Intelligent topic identification with bullet-point summaries
- Responsive UI with smooth animations
- Seamless integration with existing agents

## User Interface

### Main Page (`/megatron`)
- **Hero Section**: Animated background with Megatron branding
- **URL Input**: Clean input field for YouTube URLs with validation
- **Unleash Button**: Triggers the analysis process
- **Analysis Cards**: Interactive cards displaying extracted topics

### Analysis Cards
Each topic card includes:
- Topic title (clear and descriptive)
- Bullet-point summary of key points covered
- Two action buttons:
  - **Generate Article**: Routes to Invincible with pre-filled data
  - **Generate Script**: Routes to YouTube Scripts with pre-filled data

## API Endpoints

### GET `/api/megatron`
Returns agent status and capabilities.

**Response:**
```json
{
  "agent": "Megatron",
  "version": "1.0.0",
  "status": "ready",
  "description": "YouTube Video Analyzer and Content Generator",
  "capabilities": [
    "YouTube caption extraction",
    "Multi-language support",
    "Single vs multiple topic detection",
    "AI-powered content analysis",
    "Topic summarization",
    "Integration with Invincible for article generation",
    "Integration with YouTube Scripts for script generation"
  ]
}
```

### POST `/api/megatron/analyze`
Analyzes a YouTube video and returns structured topic data.

**Request:**
```json
{
  "youtubeUrl": "https://www.youtube.com/watch?v=VIDEO_ID"
}
```

**Response:**
```json
{
  "success": true,
  "analysis": {
    "videoTitle": "Video Title",
    "videoUrl": "https://www.youtube.com/watch?v=VIDEO_ID",
    "analysisType": "single" | "multiple",
    "topics": [
      {
        "id": "unique_id",
        "title": "Topic Title",
        "summary": [
          "Key point 1",
          "Key point 2",
          "Key point 3"
        ],
        "timestamp": "optional time range"
      }
    ]
  }
}
```

## Workflow

1. **URL Input**: User enters a YouTube URL and clicks "Unleash"
2. **Validation**: System validates the YouTube URL format
3. **Caption Extraction**: Extracts video captions using YouTubeService
4. **AI Analysis**: Gemini analyzes the transcript to identify topics
5. **Card Generation**: Creates interactive cards for each topic
6. **Content Generation**: User can generate articles or scripts from any topic

## Integration

### With Email Generator
When "Generate Email" is clicked, Megatron routes to `/email-generator` with:
- `purpose`: The selected topic title
- `audience`: Derived from video content
- `tone`: Professional tone
- `source`: "megatron" identifier
- `videoUrl`: Original video URL
- `videoTitle`: Original video title

### With YouTube Scripts
When "Generate Script" is clicked, Megatron routes to `/youtube-scripts` with:
- `title`: The selected topic title
- `brief`: Combined summary points
- `source`: "megatron" identifier
- `videoUrl`: Original video URL

### Dashboard Integration
Megatron appears in the main dashboard with:
- Custom preview animation
- Red/orange gradient theme
- Statistics tracking
- Direct navigation link

## File Structure

```
src/
├── app/
│   ├── megatron/
│   │   └── page.tsx                 # Main Megatron page
│   └── api/
│       └── megatron/
│           ├── route.ts             # Status endpoint
│           └── analyze/
│               └── route.ts         # Analysis endpoint
├── components/
│   ├── MegatronAnalysisCards.tsx    # Analysis results cards
│   └── MegatronPreview.tsx          # Dashboard preview component
└── docs/
    └── MEGATRON_AGENT.md           # This documentation
```

## Dependencies

- **YouTubeService**: For caption extraction and video metadata
- **GeminiService**: For AI-powered content analysis
- **Next.js**: For routing and API endpoints
- **Framer Motion**: For animations
- **Tailwind CSS**: For styling
- **Lucide React**: For icons

## Usage Examples

### Single Topic Video
Perfect for focused tutorials, product reviews, or educational content that covers one main subject.

### Multiple Topic Video
Ideal for tech news roundups, weekly updates, or compilation videos that cover several distinct topics.

## Error Handling

- Invalid YouTube URLs are caught and displayed to the user
- Caption extraction failures are handled gracefully
- AI analysis errors fall back to basic topic extraction
- Network errors are displayed with retry options

## Future Enhancements

- Support for video timestamps in topic cards
- Batch processing of multiple videos
- Custom topic filtering and editing
- Export functionality for analysis results
- Integration with more content generation tools

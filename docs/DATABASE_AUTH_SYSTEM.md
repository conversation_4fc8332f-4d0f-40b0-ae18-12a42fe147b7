# Database, Quota System & Google Authentication

## Overview

This document describes the comprehensive database system, quota management, and Google OAuth authentication implemented for the Invincible AI platform.

## 🗄️ Database Schema

### Core Models

#### User Model
```typescript
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  firstName     String?
  lastName      String?
  bio           String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  settings      UserSettings?
  subscription  Subscription?
  quotas        UserQuota[]
  content       Content[]
  usageHistory  UsageHistory[]
  apiKeys       ApiKey[]
}
```

#### Subscription & Quota Models
```typescript
model Subscription {
  id        String   @id @default(cuid())
  userId    String   @unique
  plan      String   // free, pro, enterprise
  status    String   // active, canceled, expired
  startDate DateTime @default(now())
  endDate   DateTime?
}

model UserQuota {
  id               String   @id @default(cuid())
  userId           String
  quotaType        String   // blog_posts, emails, social_media, youtube_scripts, invincible_research
  totalLimit       Int      // Monthly limit
  used             Int      @default(0)
  resetDate        DateTime // When quota resets
}
```

## 🔐 Authentication System

### Google OAuth Integration

The system uses NextAuth.js with Google OAuth provider:

```typescript
// Authentication configuration
GoogleProvider({
  clientId: process.env.GOOGLE_CLIENT_ID!,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
  authorization: {
    params: {
      prompt: "consent",
      access_type: "offline",
      response_type: "code"
    }
  }
})
```

### User Registration Flow

1. **Google OAuth Sign-in**: User clicks "Continue with Google"
2. **Automatic User Creation**: On first sign-in, the system creates:
   - User record with profile information
   - Default user settings
   - Free tier subscription
   - Initial quota allocations for all content types
3. **Session Management**: JWT-based sessions with NextAuth.js

### Protected Routes

Routes are protected using NextAuth middleware:

```typescript
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/generate/:path*',
    '/settings/:path*',
    '/api/generate/:path*',
    '/api/quota/:path*',
    '/api/user/:path*',

  ]
}
```

## 📊 Quota Management System

### Quota Types & Limits

| Plan | Blog Posts | Emails | Social Media | YouTube Scripts |
|------|------------|---------|--------------|-----------------|
| Free | 5/month | 10/month | 20/month | 3/month |
| Pro | 50/month | 100/month | 200/month | 25/month |
| Enterprise | Unlimited | Unlimited | Unlimited | Unlimited |

### Quota Checking Flow

1. **Pre-Generation Check**: Before content generation, API checks user quota
2. **Quota Validation**: Verifies user has remaining quota for the content type
3. **Content Generation**: Proceeds only if quota is available
4. **Quota Usage**: Decrements quota after successful generation
5. **Usage Tracking**: Logs usage in history for analytics

### API Integration

```typescript
// Check quota before generation
const quotaCheck = await QuotaManager.checkQuota(userId, 'blog_posts')
if (!quotaCheck.hasQuota) {
  return NextResponse.json(
    { error: 'Blog post quota exceeded' },
    { status: 429 }
  )
}

// Use quota after successful generation
await QuotaManager.useQuota(userId, 'blog_posts')
```

## 🔧 API Endpoints

### Authentication Endpoints

- `GET/POST /api/auth/[...nextauth]` - NextAuth.js handlers
- `GET /api/user/profile` - Get user profile and stats
- `PUT /api/user/profile` - Update user profile

### Quota Management Endpoints

- `GET /api/quota` - Get all user quotas
- `GET /api/quota?type=blog_posts` - Get specific quota
- `POST /api/quota` - Use quota (internal)

### Content Generation Endpoints (Protected)

All generation endpoints now include:
- Authentication checking
- Quota validation
- Content storage
- Usage tracking

Example: `POST /api/generate/blog`

## 🛡️ Security Features

### Authentication Security
- JWT-based sessions
- Secure OAuth flow with Google
- Route-level protection with middleware
- Session validation on API requests

### Quota Security
- Server-side quota validation
- Usage tracking and audit trails
- Automatic quota resets
- Plan-based limit enforcement

### Data Protection
- User data isolation by userId
- Secure database connections
- Input validation and sanitization
- Error handling without data exposure

## 🚀 Setup Instructions

### 1. Environment Variables

Create `.env` file with:

```bash
# Database
DATABASE_URL="file:./dev.db"

# NextAuth.js
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# API Keys
OPENROUTER_API_KEY="your-openrouter-api-key"
TAVILY_API_KEY="your-tavily-api-key"
```

### 2. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `https://yourdomain.com/api/auth/callback/google` (production)

### 3. Database Setup

```bash
# Generate Prisma client
npx prisma generate

# Create database and tables
npx prisma db push

# (Optional) View database in Prisma Studio
npx prisma studio
```

### 4. Test the System

```bash
# Run comprehensive test
node scripts/test-database-auth.mjs
```

## 📈 Usage Analytics

The system tracks comprehensive usage analytics:

### Content Analytics
- Total content pieces generated
- Content types and preferences
- Word count distributions
- Tone and style preferences

### Quota Analytics
- Quota usage patterns
- Peak usage times
- Plan upgrade triggers
- Feature adoption rates

### User Analytics
- Registration sources
- Session duration
- Feature engagement
- Retention metrics

## 🔄 Quota Reset System

Quotas automatically reset monthly:

- **Reset Date**: First day of each month at 00:00 UTC
- **Automatic Reset**: Handled by quota checking logic
- **Grace Period**: Users can continue using until next check
- **Notification**: Users notified before quota exhaustion

## 📱 Frontend Integration

### Session Management

```typescript
import { useSession } from 'next-auth/react'

function Component() {
  const { data: session, status } = useSession()
  
  if (status === "loading") return <p>Loading...</p>
  if (status === "unauthenticated") return <p>Access Denied</p>
  
  return <p>Welcome {session.user.email}!</p>
}
```

### Quota Display

```typescript
import QuotaCard from '@/components/dashboard/QuotaCard'

<QuotaCard
  quotaType="blog_posts"
  title="Blog Posts"
  icon={<BlogIcon />}
  color="bg-blue-500"
/>
```

## 🔧 Maintenance

### Database Maintenance
- Regular backups (automated in production)
- Index optimization for performance
- Cleanup of expired sessions
- Usage history archival

### Quota Maintenance
- Monitor quota usage patterns
- Adjust limits based on user feedback
- Plan upgrade recommendations
- Abuse prevention and monitoring

## 🚨 Error Handling

### Authentication Errors
- Invalid credentials → Redirect to sign-in
- Expired sessions → Automatic refresh
- OAuth failures → Error page with retry

### Quota Errors
- Quota exceeded → 429 status with upgrade suggestion
- Invalid quota type → 400 status
- Database errors → Graceful fallback

## 📊 Monitoring & Alerts

### Key Metrics to Monitor
- Authentication success/failure rates
- Quota usage vs. limits
- API response times
- Database connection health
- User registration trends

### Alert Conditions
- High authentication failure rate
- Database connection issues
- Quota system failures
- Unusual usage patterns

## 🔮 Future Enhancements

### Planned Features
- **Stripe Integration**: For paid plan upgrades
- **Team Accounts**: Shared quotas and billing
- **API Keys**: For programmatic access
- **Webhook Support**: For real-time integrations
- **Advanced Analytics**: Detailed usage insights
- **Custom Quotas**: Per-user quota adjustments

### Performance Optimizations
- Database query optimization
- Caching layer for quota checks
- Session storage optimization
- Background quota reset processing

---

## 🆘 Troubleshooting

### Common Issues

1. **"Authentication required" errors**
   - Check environment variables
   - Verify Google OAuth configuration
   - Clear browser cookies and retry

2. **Quota not updating**
   - Check database connection
   - Verify quota API endpoints
   - Run test script to validate system

3. **Database connection issues**
   - Verify DATABASE_URL in .env
   - Check file permissions for SQLite
   - Run `npx prisma db push` to recreate

4. **Google OAuth failures**
   - Verify client ID and secret
   - Check authorized redirect URIs
   - Ensure APIs are enabled in Google Console

For additional support, run the test script: `node scripts/test-database-auth.mjs` 
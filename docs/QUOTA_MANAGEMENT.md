# Quota Management System

## Overview
The application uses a comprehensive quota system to manage user access to different content generation features based on their subscription plan.

## Quota Types & Limits

### Free Plan
- **Blog Posts**: 5 per month
- **Emails**: 10 per month  
- **Social Media**: 20 per month
- **YouTube Scripts**: 3 per month
- **Content Team**: 2 per month

### Pro Plan  
- **Blog Posts**: 50 per month
- **Emails**: 100 per month
- **Social Media**: 200 per month
- **YouTube Scripts**: 25 per month
- **Content Team**: 20 per month

### Enterprise Plan
- **All Features**: Unlimited

## Common Issues

### "Content team quota exceeded" Error

**Symptom**:
```
Error: Content team quota exceeded
```

**Cause**: User has used up their monthly content team quota (2 for free plan, 20 for pro plan).

**Solution Options**:

#### Option 1: Reset Quota (Development/Testing)
```bash
# List all users to find user ID
npm run reset:quotas -- --list-users

# Check specific user's quotas
npm run reset:quotas -- <userId> --list

# Reset specific quota type
npm run reset:quotas -- <userId> content_team

# Reset all quotas for user
npm run reset:quotas -- <userId>

# Reset all quotas for all users
npm run reset:quotas -- --all
```

#### Option 2: Upgrade User Plan
```bash
# Use the quota management system to upgrade
# This would typically be done through admin interface
```

#### Option 3: Wait for Monthly Reset
Quotas automatically reset on the first day of each month.

## Quota Reset Tool Usage

### Commands

```bash
# Show help
npm run reset:quotas -- --help

# List all users
npm run reset:quotas -- --list-users

# List user's quotas
npm run reset:quotas -- <userId> --list

# Reset specific quota
npm run reset:quotas -- <userId> <quotaType>

# Reset all quotas for user
npm run reset:quotas -- <userId>

# Reset all quotas for all users
npm run reset:quotas -- --all
```

### Examples

```bash
# Check quotas for user
npm run reset:quotas -- cmcgmrw880008c94sxrpr4159 --list

# Reset content team quota
npm run reset:quotas -- cmcgmrw880008c94sxrpr4159 content_team

# Reset all quotas for development
npm run reset:quotas -- --all
```

## Error Handling Improvements

The system now provides better error messages for quota exceeded errors:

### Before
```
Error: Content team quota exceeded
```

### After
```
Content team quota exceeded (2/2 used).
Quota resets on 6/1/2025.
Upgrade to Pro for 20 monthly requests or Enterprise for unlimited access.
```

## Quota Monitoring

### Real-time Status
- Current usage vs. limits
- Reset dates
- Percentage usage
- Plan type indicators

### Database Schema
```sql
UserQuota {
  id          String   @id @default(cuid())
  userId      String
  quotaType   String   // 'content_team', 'blog_posts', etc.
  used        Int      @default(0)
  totalLimit  Int      // -1 for unlimited
  resetDate   DateTime
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

## Development Workflow

### For Testing
1. Check current quotas: `npm run reset:quotas -- --list-users`
2. Reset as needed: `npm run reset:quotas -- <userId> <quotaType>`
3. Test functionality
4. Monitor quota usage

### For Production
1. Monitor quota usage through admin dashboard
2. Set up alerts for quota approaching limits
3. Provide upgrade prompts to users
4. Handle quota exceeded gracefully

## Plan Upgrade Implementation

### Current Quota Limits Access
```javascript
import { QUOTA_LIMITS } from '@/lib/quota';

// Get limits for a plan
const freeLimits = QUOTA_LIMITS.free;
const proLimits = QUOTA_LIMITS.pro;
const enterpriseLimits = QUOTA_LIMITS.enterprise;
```

### Upgrading User Plan
```javascript
import { QuotaManager } from '@/lib/quota';

// Upgrade user to pro plan
await QuotaManager.upgradeUserPlan(userId, 'pro');

// Upgrade to enterprise
await QuotaManager.upgradeUserPlan(userId, 'enterprise');
```

## Files Involved

### Core Files
- `src/lib/quota.ts` - Main quota management logic
- API routes with quota checks (various generation endpoints)
- `scripts/reset-quotas.mjs` - Quota reset tool

### Database
- `prisma/schema.prisma` - UserQuota and Subscription models
- User quotas are tracked per quota type
- Automatic monthly resets

### Frontend
- Enhanced error handling in generation pages
- Better error messages for quota exceeded

## Security Considerations

### Quota Bypass Prevention
- Server-side validation only
- No client-side quota checks
- Secure session validation
- Audit trail in usage history

### Rate Limiting
- Additional rate limiting beyond quotas
- Prevents abuse and ensures fair usage
- Protects backend resources

## Monitoring & Analytics

### Usage Tracking
```javascript
// Track usage in UsageHistory table
await prisma.usageHistory.create({
  data: {
    userId,
    action: 'content_generated',
    type: quotaType,
    metadata: JSON.stringify({
      quotaUsed: used + 1,
      quotaLimit: limit
    })
  }
});
```

### Metrics to Track
- Quota usage by plan type
- Conversion rates from quota exceeded to upgrades
- Peak usage times
- Feature adoption rates

## Troubleshooting

### Common Issues

1. **Quota not resetting**: Check resetDate and server timezone
2. **Incorrect limits**: Verify plan assignment and QUOTA_LIMITS
3. **Database sync issues**: Check UserQuota records
4. **Session issues**: Verify authentication and user ID

### Debug Commands
```bash
# Check user subscription
npm run reset:quotas -- <userId> --list

# Verify quota calculations
# Check database directly if needed

# Reset for testing
npm run reset:quotas -- --all
```

## Best Practices

### For Development
- Reset quotas before testing features
- Use development-specific quota limits
- Test quota exceeded scenarios
- Verify error handling

### For Production
- Monitor quota usage patterns
- Set up automated alerts
- Provide clear upgrade paths
- Handle edge cases gracefully

### For Users
- Clear quota information in UI
- Progress indicators
- Upgrade prompts when appropriate
- Transparent pricing and limits

## Summary

The quota system ensures fair usage while providing clear upgrade paths. The reset tool allows for easy development and testing, while the enhanced error handling provides better user experience when quotas are exceeded. 
"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { motion } from "framer-motion";
import { Calendar, Clock, Globe, User, Tag } from "lucide-react";

// Enhanced blog post display component with modern styling
export default function StyleDisplay({ 
  title, 
  content, 
  metadata = {},
  showMetadata = true,
  variant = "default" // "default", "minimal", "magazine"
}) {
  const formatDate = (dateString) => {
    if (!dateString) return new Date().toLocaleDateString();
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReadingTime = (content) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(/\s+/).length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "minimal":
        return {
          container: "min-h-screen bg-white",
          wrapper: "max-w-2xl mx-auto px-6 py-16",
          article: "bg-white",
          header: "mb-12 text-center",
          title: "text-4xl font-light text-gray-900 mb-6 leading-tight",
          meta: "text-sm text-gray-500 space-x-4"
        };
      case "magazine":
        return {
          container: "min-h-screen bg-gradient-to-br from-slate-50 to-gray-100",
          wrapper: "max-w-4xl mx-auto px-8 py-12",
          article: "bg-white shadow-2xl rounded-3xl overflow-hidden",
          header: "bg-gradient-to-r from-indigo-600 to-purple-600 text-white p-12",
          title: "text-5xl font-bold mb-6 leading-tight",
          meta: "text-indigo-100 space-x-6"
        };
      default:
        return {
          container: "min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",
          wrapper: "max-w-4xl mx-auto px-6 py-12",
          article: "bg-white/90 backdrop-blur-sm shadow-xl rounded-2xl overflow-hidden border border-blue-100",
          header: "bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-10",
          title: "text-4xl font-bold mb-4 leading-tight",
          meta: "text-blue-100 space-x-4"
        };
    }
  };

  const styles = getVariantStyles();

  return (
    <div className={styles.container}>
      <div className={styles.wrapper}>
        <motion.article 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className={styles.article}
        >
          {/* Header */}
          <header className={styles.header}>
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className={styles.title}
            >
              {title}
            </motion.h1>
            
            {showMetadata && (
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.6 }}
                className={`flex flex-wrap items-center ${styles.meta}`}
              >
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(metadata.generatedAt)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{getReadingTime(content)} min read</span>
                </div>
                {metadata.wordCount && (
                  <div className="flex items-center space-x-1">
                    <Tag className="w-4 h-4" />
                    <span>{metadata.wordCount.toLocaleString()} words</span>
                  </div>
                )}
                {metadata.language && (
                  <div className="flex items-center space-x-1">
                    <Globe className="w-4 h-4" />
                    <span>{metadata.language.toUpperCase()}</span>
                  </div>
                )}
              </motion.div>
            )}
          </header>

          {/* Content */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="p-10"
          >
            <div className="prose prose-lg max-w-none">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  h1: ({ children }) => (
                    <h1 className="text-3xl font-bold text-gray-900 mb-6 mt-8 pb-2 border-b border-gray-200">
                      {children}
                    </h1>
                  ),
                  h2: ({ children }) => (
                    <h2 className="text-2xl font-semibold text-gray-800 mb-4 mt-8">
                      {children}
                    </h2>
                  ),
                  h3: ({ children }) => (
                    <h3 className="text-xl font-semibold text-gray-700 mb-3 mt-6">
                      {children}
                    </h3>
                  ),
                  h4: ({ children }) => (
                    <h4 className="text-lg font-semibold text-gray-700 mb-2 mt-4">
                      {children}
                    </h4>
                  ),
                  p: ({ children }) => (
                    <p className="text-gray-700 leading-8 mb-6 text-lg">
                      {children}
                    </p>
                  ),
                  a: ({ href, children }) => (
                    <a 
                      href={href} 
                      target="_blank" 
                      rel="noreferrer" 
                      className="text-blue-600 hover:text-blue-800 underline underline-offset-2 transition-colors"
                    >
                      {children}
                    </a>
                  ),
                  ul: ({ children }) => (
                    <ul className="list-disc list-outside ml-6 text-gray-700 space-y-2 mb-6 text-lg">
                      {children}
                    </ul>
                  ),
                  ol: ({ children }) => (
                    <ol className="list-decimal list-outside ml-6 text-gray-700 space-y-2 mb-6 text-lg">
                      {children}
                    </ol>
                  ),
                  li: ({ children }) => (
                    <li className="leading-8 mb-1">{children}</li>
                  ),
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-blue-400 pl-6 italic text-gray-600 my-8 bg-blue-50/50 py-4 rounded-r-xl">
                      {children}
                    </blockquote>
                  ),
                  code: ({ children }) => (
                    <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">
                      {children}
                    </code>
                  ),
                  pre: ({ children }) => (
                    <pre className="bg-gray-900 text-gray-100 p-6 rounded-xl overflow-x-auto border border-gray-200 my-6">
                      {children}
                    </pre>
                  ),
                  table: ({ children }) => (
                    <div className="overflow-x-auto my-8">
                      <table className="min-w-full border border-gray-200 rounded-lg">
                        {children}
                      </table>
                    </div>
                  ),
                  th: ({ children }) => (
                    <th className="px-6 py-3 text-left bg-gray-50 text-gray-900 border-b border-gray-200 font-semibold">
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td className="px-6 py-4 text-gray-700 border-b border-gray-100">
                      {children}
                    </td>
                  ),
                  strong: ({ children }) => (
                    <strong className="font-semibold text-gray-900">{children}</strong>
                  ),
                  em: ({ children }) => (
                    <em className="italic text-gray-700">{children}</em>
                  )
                }}
              >
                {content}
              </ReactMarkdown>
            </div>
          </motion.div>

          {/* Footer with metadata */}
          {showMetadata && (metadata.sourceUrls && metadata.sourceUrls.length > 0 || metadata.generationMethod === 'openrouter_knowledge') && (
            <motion.footer 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.6 }}
              className="bg-gray-50 p-8 border-t border-gray-200"
            >
              {metadata.generationMethod === 'openrouter_knowledge' ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Generation Method</h3>
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-amber-600">🧠</span>
                      <span className="text-amber-800 font-medium">AI Knowledge-Based Generation</span>
                    </div>
                    <p className="text-amber-700 text-sm mt-2">
                      This article was generated using advanced AI knowledge and reasoning capabilities.
                      While web research was not available, the content is based on comprehensive AI training data.
                    </p>
                  </div>
                </div>
              ) : (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Sources</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {metadata.sourceUrls.slice(0, 6).map((source, index) => (
                      <a
                        key={index}
                        href={source.url}
                        target="_blank"
                        rel="noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 underline truncate block"
                      >
                        {source.title || source.url}
                      </a>
                    ))}
                  </div>
                  {metadata.sourceUrls.length > 6 && (
                    <p className="text-sm text-gray-500 mt-3">
                      And {metadata.sourceUrls.length - 6} more sources...
                    </p>
                  )}
                </div>
              )}
            </motion.footer>
          )}
        </motion.article>
      </div>
    </div>
  );
}

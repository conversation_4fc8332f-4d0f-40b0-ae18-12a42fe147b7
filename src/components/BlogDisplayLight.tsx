"use client";

import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

type Props = {
  title?: string;
  markdown: string;
};

// Minimal light-blue blog display for generated markdown
export default function BlogDisplayLight({ title, markdown }: Props) {
  return (
    <div className="min-h-screen" style={{ background: "linear-gradient(180deg, #E6F6FF 0%, #FFFFFF 60%)" }}>
      <div className="max-w-3xl mx-auto px-5 py-12">
        <header className="mb-10">
          {title && (
            <h1 className="text-3xl font-bold text-sky-800 tracking-tight">{title}</h1>
          )}
        </header>
        <article className="bg-white/80 backdrop-blur-md border border-sky-200 rounded-2xl shadow-xl p-6">
          <div className="prose max-w-none">
            <ReactMarkdown
              remarkPlugins={[remarkGfm]}
              components={{
                h1: ({ children }) => (
                  <h1 className="text-3xl font-bold text-sky-900 mb-4">{children}</h1>
                ),
                h2: ({ children }) => (
                  <h2 className="text-2xl font-semibold text-sky-800 mt-8 mb-3">{children}</h2>
                ),
                h3: ({ children }) => (
                  <h3 className="text-xl font-semibold text-sky-700 mt-6 mb-2">{children}</h3>
                ),
                p: ({ children }) => (
                  <p className="text-slate-700 leading-7 mb-4">{children}</p>
                ),
                a: ({ href, children }) => (
                  <a href={href} target="_blank" rel="noreferrer" className="text-sky-700 underline underline-offset-2">
                    {children}
                  </a>
                ),
                ul: ({ children }) => (
                  <ul className="list-disc list-outside ml-6 text-slate-700 space-y-2 mb-4">{children}</ul>
                ),
                ol: ({ children }) => (
                  <ol className="list-decimal list-outside ml-6 text-slate-700 space-y-2 mb-4">{children}</ol>
                ),
                li: ({ children }) => <li className="leading-7">{children}</li>,
                blockquote: ({ children }) => (
                  <blockquote className="border-l-4 border-sky-300 pl-4 italic text-slate-600 my-6 bg-sky-50/60 py-3 rounded-r-xl">
                    {children}
                  </blockquote>
                ),
                code: ({ children }) => (
                  <code className="bg-sky-50 text-sky-800 px-1.5 py-0.5 rounded text-sm font-mono">{children}</code>
                ),
                pre: ({ children }) => (
                  <pre className="bg-sky-50 text-sky-900 p-4 rounded-lg overflow-x-auto border border-sky-200">{children}</pre>
                ),
                table: ({ children }) => (
                  <div className="overflow-x-auto my-6">
                    <table className="min-w-full border border-sky-200 rounded-lg">
                      {children}
                    </table>
                  </div>
                ),
                th: ({ children }) => (
                  <th className="px-4 py-2 text-left bg-sky-50 text-sky-900 border-b border-sky-200">{children}</th>
                ),
                td: ({ children }) => (
                  <td className="px-4 py-2 text-slate-700 border-b border-sky-100">{children}</td>
                )
              }}
            >
              {markdown}
            </ReactMarkdown>
          </div>
        </article>
      </div>
    </div>
  );
}



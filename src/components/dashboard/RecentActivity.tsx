import { motion } from 'framer-motion'
import { Clock, CheckCircle, AlertCircle, Loader2, FileText, Mail, Twitter, Youtube, PenTool } from 'lucide-react'

interface Activity {
  id: string
  type: 'email' | 'tweet' | 'youtube' | 'document'
  title: string
  time: string
  status: 'completed' | 'processing' | 'failed'
  wordCount?: number
  quality?: number
}

const getIcon = (type: string) => {
  switch (type) {
    case 'email': return Mail
    case 'tweet': return Twitter
    case 'youtube': return Youtube
    default: return FileText
  }
}

const getGradient = (type: string) => {
  switch (type) {
    case 'email': return 'from-purple-500/20 to-pink-600/20'
    case 'tweet': return 'from-sky-500/20 to-blue-600/20'
    case 'youtube': return 'from-red-500/20 to-rose-600/20'
    default: return 'from-gray-500/20 to-gray-600/20'
  }
}

interface RecentActivityProps {
  activities?: Activity[]
}

export default function RecentActivity({ activities }: RecentActivityProps) {
  const defaultActivities: Activity[] = [
    { id: '2', type: 'email', title: 'Product Launch Announcement', time: '4 hours ago', status: 'completed', wordCount: 450 },
    { id: '3', type: 'tweet', title: 'Web3 Technology Thread', time: '6 hours ago', status: 'completed' },
    { id: '4', type: 'youtube', title: 'Tutorial: Getting Started with React', time: '8 hours ago', status: 'processing' },
    { id: '6', type: 'email', title: 'Newsletter Campaign', time: '1 day ago', status: 'completed', wordCount: 800 },
  ]

  const items = activities || defaultActivities

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="glass-card p-6"
    >
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
          <Clock className="w-5 h-5 text-blue-400" />
          Recent Activity
        </h3>
        <button className="text-sm text-blue-400 hover:text-blue-300 transition-colors">
          View All
        </button>
      </div>

      <div className="space-y-3">
        {items.map((activity, index) => {
          const Icon = getIcon(activity.type)
          
          return (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-4 hover:bg-white/5 rounded-lg transition-all duration-200 cursor-pointer group"
            >
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${getGradient(activity.type)} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                
                <div className="flex-1">
                  <h4 className="text-white font-medium group-hover:text-blue-300 transition-colors">
                    {activity.title}
                  </h4>
                  <div className="flex items-center gap-4 mt-1">
                    <p className="text-gray-400 text-sm">{activity.time}</p>
                    {activity.wordCount && (
                      <p className="text-gray-500 text-xs">{activity.wordCount} words</p>
                    )}
                    {activity.quality && (
                      <div className="flex items-center gap-1">
                        <div className="w-1 h-1 bg-gray-600 rounded-full" />
                        <p className="text-gray-500 text-xs">Quality: {activity.quality}/10</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className={`px-3 py-1 rounded-full text-xs font-medium flex items-center gap-1.5 ${
                  activity.status === 'completed' 
                    ? 'bg-green-500/20 text-green-400' 
                    : activity.status === 'processing'
                    ? 'bg-yellow-500/20 text-yellow-400'
                    : 'bg-red-500/20 text-red-400'
                }`}>
                  {activity.status === 'completed' && <CheckCircle className="w-3 h-3" />}
                  {activity.status === 'processing' && <Loader2 className="w-3 h-3 animate-spin" />}
                  {activity.status === 'failed' && <AlertCircle className="w-3 h-3" />}
                  {activity.status}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      <div className="mt-6 pt-6 border-t border-white/10">
        <div className="flex items-center justify-between text-sm">
          <p className="text-gray-400">
            Total content generated today: <span className="text-white font-medium">24 pieces</span>
          </p>
          <p className="text-gray-400">
            Average quality: <span className="text-green-400 font-medium">9.1/10</span>
          </p>
        </div>
      </div>
    </motion.div>
  )
} 
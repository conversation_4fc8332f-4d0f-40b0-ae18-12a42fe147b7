'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import ProfileButton from '@/components/ProfileButton';

export default function SaaSHeader() {
  const pathname = usePathname();
  const [mobileOpen, setMobileOpen] = useState(false);


  const NavLink = ({ href, label }: { href: string; label: string }) => (
    <Link
      href={href}
      className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
        pathname === href
          ? 'text-slate-900 bg-slate-100'
          : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
      }`}
    >
      {label}
    </Link>
  );

  return (
    <header className="sticky top-0 z-50 border-b bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Brand */}
          <div className="flex items-center gap-3">
            <Link href="/" className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-md bg-gradient-to-br from-violet-600 to-indigo-600" />
              <span className="text-base font-semibold text-slate-900">AI Content Studio</span>
            </Link>
          </div>

          {/* Desktop nav */}
          <nav className="hidden md:flex items-center gap-2">
            <NavLink href="/dashboard" label="Dashboard" />
            <NavLink href="/content" label="Content" />
            <NavLink href="/email-generator" label="Email" />
            <NavLink href="/social-media-generator" label="Social" />
            <NavLink href="/youtube-script" label="YouTube" />
            <NavLink href="/settings" label="Settings" />
          </nav>

          {/* Right actions */}
          <div className="hidden md:flex items-center gap-3">
            <Link
              href="/email-generator"
              className="inline-flex items-center rounded-lg bg-slate-900 px-3 py-2 text-sm font-medium text-white hover:bg-slate-800"
            >
              New Email
            </Link>
            <ProfileButton userProfile={null} />
          </div>

          {/* Mobile */}
          <button
            onClick={() => setMobileOpen(v => !v)}
            className="md:hidden inline-flex items-center justify-center rounded-lg p-2 text-slate-600 hover:bg-slate-100 hover:text-slate-900"
            aria-label="Toggle menu"
          >
            <svg className="h-6 w-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile panel */}
      {mobileOpen && (
        <div className="md:hidden border-t bg-white">
          <div className="mx-auto max-w-7xl px-4 py-3 space-y-1">
            <NavLink href="/dashboard" label="Dashboard" />
            <NavLink href="/content" label="Content" />
            <NavLink href="/email-generator" label="Email" />
            <NavLink href="/social-media-generator" label="Social" />
            <NavLink href="/youtube-script" label="YouTube" />
            <NavLink href="/settings" label="Settings" />
          </div>
        </div>
      )}
    </header>
  );
}



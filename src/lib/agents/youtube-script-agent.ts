import { OpenRouterService } from '@/lib/openrouter'
import { YouTubeService } from '@/lib/youtube-service'
import { enhancedTavilySearch } from '@/lib/tools/tavily-search'

interface YouTubeScriptRequest {
  topic: string
  duration: string
  style: string
  targetAudience: string
  additionalNotes?: string
}

interface YouTubeScript {
  title: string
  hook: string
  introduction: string
  mainContent: string
  conclusion: string
  fullScript: string
  metadata: {
    estimatedLength: string
    wordCount: number
    keyTopics: string[]
    engagementScore: number
    researchVideos: VideoData[]
  }
}

interface VideoData {
  id: string
  title: string
  channel: string
  views: number
  duration: string
  url: string
  captions: CaptionSegment[]
  publishedTime: string
}

interface CaptionSegment {
  text: string
  start: number
  duration: number
}

interface ResearchData {
  youtubeVideos: VideoData[]
  tavilyResults: TavilySearchResult[]
  captionAnalysis: CaptionAnalysis
}

interface TavilySearchResult {
  title: string
  url: string
  content: string
  score: number
}

interface CaptionAnalysis {
  commonPatterns: string[]
  engagementTechniques: string[]
  structuralElements: string[]
  toneAnalysis: string
  keyInsights: string[]
}

export class YouTubeScriptAgent {
  private openRouter: OpenRouterService
  private youtubeService: YouTubeService
  private agentName = 'YouTube Script Generator'

  constructor() {
    this.openRouter = new OpenRouterService({
      model: 'moonshotai/kimi-k2',
      temperature: 0.7,
      maxTokens: 16000 // Double the tokens as requested
    })
    this.youtubeService = new YouTubeService()
  }

  /**
   * Generate a complete YouTube script using enhanced research workflow
   */
  async generateScript(request: YouTubeScriptRequest): Promise<YouTubeScript> {
    console.log(`🎬 ${this.agentName}: Starting enhanced script generation for "${request.topic}"`)

    try {
      // Step 1: Search YouTube videos for exact topic
      console.log(`🔍 Step 1: Searching YouTube for "${request.topic}"`)
      const youtubeVideos = await this.searchYouTubeVideos(request.topic)

      // Step 2: Extract captions using Supadata API
      console.log(`📝 Step 2: Extracting captions from ${youtubeVideos.length} videos`)
      const videosWithCaptions = await this.extractCaptions(youtubeVideos)

      // Step 3: Analyze captions to understand script patterns
      console.log(`🧠 Step 3: Analyzing caption patterns`)
      const captionAnalysis = await this.analyzeCaptions(videosWithCaptions)

      // Step 4: Conduct Tavily web search for additional context
      console.log(`🌐 Step 4: Conducting web search with Tavily`)
      const tavilyResults = await this.searchWithTavily(request.topic)

      // Step 5: Combine all research data
      const researchData: ResearchData = {
        youtubeVideos: videosWithCaptions,
        tavilyResults,
        captionAnalysis
      }

      // Step 6: Generate the complete script using all research
      console.log(`✍️ Step 6: Generating script with comprehensive research`)
      const script = await this.createScript(request, researchData)

      // Step 7: Generate optimized title
      const title = await this.generateTitle(request.topic, request.style)

      console.log(`✅ ${this.agentName}: Enhanced script generation completed`)

      return {
        ...script,
        title,
        metadata: {
          estimatedLength: this.calculateDuration(script.fullScript, request.duration),
          wordCount: this.countWords(script.fullScript),
          keyTopics: this.extractKeyTopics(script.fullScript),
          engagementScore: this.calculateEngagementScore(script.fullScript),
          researchVideos: videosWithCaptions.slice(0, 5) // Top 5 videos for reference
        }
      }
    } catch (error) {
      console.error(`❌ ${this.agentName}: Enhanced script generation failed:`, error)
      throw new Error(`Script generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Search YouTube videos for exact topic using YouTube Service
   */
  private async searchYouTubeVideos(topic: string): Promise<VideoData[]> {
    console.log(`🔍 Searching YouTube for: ${topic}`)

    try {
      const searchResult = await this.youtubeService.searchVideos(topic, 10)

      if (!searchResult || !searchResult.videos || searchResult.videos.length === 0) {
        throw new Error('No videos found for the given topic')
      }

      // Get top 10 videos
      const videos = searchResult.videos.slice(0, 10)
      const videoData: VideoData[] = []

      for (const video of videos) {
        try {
          videoData.push({
            id: video.id,
            title: video.title || 'Unknown Title',
            channel: video.channelTitle || 'Unknown Channel',
            views: parseInt(video.viewCount) || 0,
            duration: video.duration || 'Unknown',
            url: `https://youtube.com/watch?v=${video.id}`,
            captions: [], // Will be filled by extractCaptions
            publishedTime: video.publishedAt || 'Unknown'
          })
        } catch (error) {
          console.warn(`Failed to process video ${video.id}:`, error)
        }
      }

      console.log(`✅ Found ${videoData.length} videos for analysis`)
      return videoData
    } catch (error) {
      console.error('YouTube search failed:', error)
      // Return fallback data
      return [{
        id: 'fallback',
        title: `Video about ${topic}`,
        channel: 'Example Channel',
        views: 100000,
        duration: '10:00',
        url: 'https://youtube.com/watch?v=example',
        captions: [],
        publishedTime: '1 week ago'
      }]
    }
  }

  /**
   * Extract captions using existing YouTubeService with Supadata integration
   */
  private async extractCaptions(videos: VideoData[]): Promise<VideoData[]> {
    console.log(`📝 Extracting captions from ${videos.length} videos using YouTubeService`)

    const videosWithCaptions = await Promise.all(
      videos.map(async (video) => {
        try {
          // Use existing YouTubeService which has Supadata integration and built-in fallbacks
          const captions = await this.youtubeService.extractCaptions(video.id, 'en')

          // Convert to our format
          const captionSegments: CaptionSegment[] = captions.map((caption, index) => ({
            text: caption.text || '',
            start: caption.start || index * 3,
            duration: caption.duration || 3
          }))

          return {
            ...video,
            captions: captionSegments.slice(0, 50) // Limit to first 50 segments
          }
        } catch (error) {
          console.warn(`Failed to extract captions for ${video.id}:`, error)
          // Return video with placeholder caption
          return {
            ...video,
            captions: [{
              text: `Content about ${video.title}`,
              start: 0,
              duration: 5
            }]
          }
        }
      })
    )

    const totalCaptions = videosWithCaptions.reduce((sum, video) => sum + video.captions.length, 0)
    console.log(`✅ Extracted ${totalCaptions} caption segments from ${videosWithCaptions.length} videos`)

    return videosWithCaptions
  }

  /**
   * Analyze captions to understand script writing patterns
   */
  private async analyzeCaptions(videos: VideoData[]): Promise<CaptionAnalysis> {
    console.log(`🧠 Analyzing caption patterns from ${videos.length} videos`)

    // Combine all captions into analysis text
    const allCaptions = videos.flatMap(video =>
      video.captions.map(caption => caption.text)
    ).join(' ')

    const analysisPrompt = `Analyze these YouTube video captions to understand successful script writing patterns:

CAPTIONS TO ANALYZE:
${allCaptions.substring(0, 8000)} // Limit to avoid token overflow

Please provide a comprehensive analysis in the following format:

COMMON PATTERNS:
- List the most common structural patterns you see
- Identify recurring phrases or techniques
- Note how videos typically start and end

ENGAGEMENT TECHNIQUES:
- What hooks and attention-grabbers are used?
- How do creators maintain viewer interest?
- What call-to-action patterns appear?

STRUCTURAL ELEMENTS:
- How are topics introduced and developed?
- What transition techniques are used?
- How is information organized and presented?

TONE ANALYSIS:
- What is the overall tone and style?
- How formal or casual is the language?
- What personality traits come through?

KEY INSIGHTS:
- What makes these scripts effective?
- What patterns should be replicated?
- What unique elements stand out?

Focus on actionable insights for script writing.`

    try {
      const result = await this.openRouter.generateYouTubeContent(
        analysisPrompt,
        'You are an expert YouTube script analyst. Analyze the provided captions to identify successful patterns and techniques.',
        { temperature: 0.3, maxTokens: 4000 },
        'Caption Analysis'
      )

      return this.parseCaptionAnalysis(result.response)
    } catch (error) {
      console.warn('Caption analysis failed, using fallback analysis')
      return {
        commonPatterns: ['Strong opening hooks', 'Clear value propositions', 'Structured content delivery'],
        engagementTechniques: ['Questions to audience', 'Teasing upcoming content', 'Personal anecdotes'],
        structuralElements: ['Introduction with hook', 'Main content in segments', 'Strong conclusion with CTA'],
        toneAnalysis: 'Conversational and engaging with authority',
        keyInsights: ['Immediate value delivery', 'Consistent engagement', 'Clear call-to-actions']
      }
    }
  }

  /**
   * Search with Tavily for additional context using existing enhanced search
   */
  private async searchWithTavily(topic: string): Promise<TavilySearchResult[]> {
    console.log(`🌐 Searching with Tavily for: ${topic}`)

    try {
      // Use existing enhanced Tavily search
      const searchResults: any = await enhancedTavilySearch.search(
        `${topic} YouTube content creation tips trends 2025`,
        {
          maxResults: 10,
          searchDepth: 'advanced',
          includeAnswer: true,
          includeRawContent: false,
          includeImages: false,
          temporalFocus: 'current'
        }
      )

      if (!searchResults || !searchResults.results) {
        throw new Error('No results from enhanced Tavily search')
      }

      const results: TavilySearchResult[] = searchResults.results.map((result: any) => ({
        title: result.title || '',
        url: result.url || '',
        content: result.content || result.raw_content || '',
        score: result.score || 0
      }))

      console.log(`✅ Found ${results.length} Tavily search results`)
      return results
    } catch (error) {
      console.warn('Enhanced Tavily search failed:', error)
      return [{
        title: `${topic} - Content Creation Guide`,
        url: 'https://example.com',
        content: `Comprehensive guide about ${topic} for content creators`,
        score: 0.8
      }]
    }
  }

  /**
   * Create the complete script structure using enhanced research
   */
  private async createScript(request: YouTubeScriptRequest, research: ResearchData): Promise<Omit<YouTubeScript, 'title' | 'metadata'>> {
    const scriptPrompt = this.buildScriptPrompt(request, research)

    const result = await this.openRouter.generateYouTubeContent(
      scriptPrompt,
      '',
      { 
        temperature: 0.7, 
        maxTokens: 16000 // Double the tokens
      },
      'Script Generation'
    )

    return this.parseScriptResponse(result.response, request)
  }

  /**
   * Build comprehensive script generation prompt using enhanced research
   */
  private buildScriptPrompt(request: YouTubeScriptRequest, research: ResearchData): string {
    const durationGuidance = this.getDurationGuidance(request.duration)

    // Prepare research summaries
    const topVideos = research.youtubeVideos.slice(0, 5).map(video =>
      `"${video.title}" by ${video.channel} (${video.views.toLocaleString()} views)`
    ).join('\n')

    const captionInsights = research.captionAnalysis
    const webInsights = research.tavilyResults.slice(0, 3).map(result =>
      `${result.title}: ${result.content.substring(0, 200)}...`
    ).join('\n')

    return `Create a compelling YouTube script about "${request.topic}" using comprehensive research data:

SCRIPT REQUIREMENTS:
- Duration: ${request.duration}
- Style: ${request.style}
- Target Audience: ${request.targetAudience}
- Additional Notes: ${request.additionalNotes || 'None'}

YOUTUBE VIDEO RESEARCH:
Top performing videos analyzed:
${topVideos}

CAPTION ANALYSIS INSIGHTS:
Common Patterns: ${captionInsights.commonPatterns.join(', ')}
Engagement Techniques: ${captionInsights.engagementTechniques.join(', ')}
Structural Elements: ${captionInsights.structuralElements.join(', ')}
Tone Analysis: ${captionInsights.toneAnalysis}
Key Success Factors: ${captionInsights.keyInsights.join(', ')}

WEB RESEARCH INSIGHTS:
${webInsights}

SCRIPT STRUCTURE (Based on successful patterns):
1. HOOK (First 15 seconds): Use proven attention-grabbing techniques from analysis
2. INTRODUCTION (Next 30-45 seconds): Establish credibility and preview value
3. MAIN CONTENT (${durationGuidance.mainContentTime}): Deliver core value using successful structural patterns
4. CONCLUSION (Final 60-90 seconds): Strong CTA based on engagement analysis

CONTENT GUIDELINES (Based on Research):
- Apply the successful patterns identified in caption analysis
- Use engagement techniques that work for this topic
- Incorporate the tone and style that resonates with the audience
- Include specific examples and actionable advice
- Reference current trends and 2025 context
- Structure content using proven successful formats
- Apply the key insights from top-performing videos

TARGET WORD COUNT: ${durationGuidance.wordCount} words

Please provide the complete script with clear section markers:
[HOOK]
[INTRODUCTION]
[MAIN CONTENT]
[CONCLUSION]

Make this script compelling, research-backed, and optimized for YouTube success based on the analysis of successful content in this niche.`
  }

  /**
   * Generate optimized title
   */
  private async generateTitle(topic: string, style: string): Promise<string> {
    const titlePrompt = `Create 5 compelling YouTube titles for a ${style} video about "${topic}". 

Requirements:
- Optimized for YouTube algorithm and click-through rate
- Include power words and emotional triggers
- Reference 2025 for freshness
- 60 characters or less for mobile optimization
- Match the ${style} style

Provide 5 options and mark the best one with [BEST].`

    try {
      const result = await this.openRouter.generateYouTubeContent(
        titlePrompt,
        '',
        { temperature: 0.8, maxTokens: 500 },
        'Title Generation'
      )

      return this.extractBestTitle(result.response) || `${topic}: Complete 2025 Guide`
    } catch (error) {
      return `${topic}: Complete 2025 Guide`
    }
  }

  /**
   * Parse caption analysis from AI response
   */
  private parseCaptionAnalysis(response: string): CaptionAnalysis {
    const patternsMatch = response.match(/COMMON PATTERNS:?\s*(.*?)(?=ENGAGEMENT|$)/is)
    const engagementMatch = response.match(/ENGAGEMENT TECHNIQUES:?\s*(.*?)(?=STRUCTURAL|$)/is)
    const structuralMatch = response.match(/STRUCTURAL ELEMENTS:?\s*(.*?)(?=TONE|$)/is)
    const toneMatch = response.match(/TONE ANALYSIS:?\s*(.*?)(?=KEY INSIGHTS|$)/is)
    const insightsMatch = response.match(/KEY INSIGHTS:?\s*(.*?)$/is)

    return {
      commonPatterns: this.extractListItems(patternsMatch?.[1] || ''),
      engagementTechniques: this.extractListItems(engagementMatch?.[1] || ''),
      structuralElements: this.extractListItems(structuralMatch?.[1] || ''),
      toneAnalysis: toneMatch?.[1]?.trim() || 'Conversational and engaging',
      keyInsights: this.extractListItems(insightsMatch?.[1] || '')
    }
  }

  /**
   * Parse view count from YouTube format
   */
  private parseViewCount(viewText: string): number {
    const cleanText = viewText.replace(/[^\d.KMB]/gi, '')
    const number = parseFloat(cleanText)

    if (cleanText.includes('K')) return Math.floor(number * 1000)
    if (cleanText.includes('M')) return Math.floor(number * 1000000)
    if (cleanText.includes('B')) return Math.floor(number * 1000000000)

    return Math.floor(number) || 0
  }

  /**
   * Parse script response into structured format
   */
  private parseScriptResponse(response: string, request: YouTubeScriptRequest): Omit<YouTubeScript, 'title' | 'metadata'> {
    const hookMatch = response.match(/\[HOOK\](.*?)(?=\[INTRODUCTION\]|$)/is)
    const introMatch = response.match(/\[INTRODUCTION\](.*?)(?=\[MAIN CONTENT\]|$)/is)
    const mainMatch = response.match(/\[MAIN CONTENT\](.*?)(?=\[CONCLUSION\]|$)/is)
    const conclusionMatch = response.match(/\[CONCLUSION\](.*?)$/is)

    const hook = this.cleanSection(hookMatch?.[1] || this.generateFallbackHook(request.topic))
    const introduction = this.cleanSection(introMatch?.[1] || this.generateFallbackIntro(request.topic))
    const mainContent = this.cleanSection(mainMatch?.[1] || this.generateFallbackMain(request.topic))
    const conclusion = this.cleanSection(conclusionMatch?.[1] || this.generateFallbackConclusion(request.topic))

    const fullScript = this.buildFullScript(hook, introduction, mainContent, conclusion)

    return {
      hook,
      introduction,
      mainContent,
      conclusion,
      fullScript
    }
  }

  /**
   * Helper methods for script processing
   */
  private getDurationGuidance(duration: string) {
    const guides = {
      '1-3': { wordCount: 2400, mainContentTime: '1-2 minutes' },
      '3-7': { wordCount: 5600, mainContentTime: '3-5 minutes' },
      '7-15': { wordCount: 12000, mainContentTime: '6-12 minutes' },
      '15+': { wordCount: 24000, mainContentTime: '12+ minutes' }
    }
    return guides[duration as keyof typeof guides] || guides['3-7']
  }

  private extractListItems(text: string): string[] {
    const items = text.match(/(?:^|\n)\s*[-•*]\s*(.+)/gm) || 
                  text.match(/(?:^|\n)\s*\d+\.\s*(.+)/gm) ||
                  text.split('\n').filter(line => line.trim().length > 10)
    
    return items.slice(0, 5).map(item => item.replace(/^[-•*\d.\s]+/, '').trim())
  }

  private extractBestTitle(response: string): string | null {
    const bestMatch = response.match(/\[BEST\]\s*(.+)/i)
    if (bestMatch) return bestMatch[1].trim()

    const titles = response.match(/^.+$/gm)?.filter(line => 
      line.length > 10 && line.length < 80 && !line.includes('[')
    )
    return titles?.[0]?.trim() || null
  }

  private cleanSection(content: string): string {
    return content.trim()
      .replace(/^\[.*?\]\s*/, '')
      .replace(/\n\s*\n/g, '\n\n')
      .trim()
  }

  private buildFullScript(hook: string, intro: string, main: string, conclusion: string): string {
    return `## 🎯 Hook (0:00-0:15)
${hook}

## 👋 Introduction (0:15-1:00)
${intro}

## 📚 Main Content
${main}

## 🎬 Conclusion
${conclusion}

---
*Generated with AI-powered YouTube Script Generator using OpenRouter + Kimi-K2*`
  }

  private generateFallbackHook(topic: string): string {
    return `What if I told you that everything you think you know about ${topic} is about to change? In the next few minutes, I'm going to show you exactly what you need to know to master ${topic} in 2025.`
  }

  private generateFallbackIntro(topic: string): string {
    return `Welcome back to the channel! Today we're diving deep into ${topic}, and I promise by the end of this video, you'll have a complete understanding that most people take years to develop. Let's get started.`
  }

  private generateFallbackMain(topic: string): string {
    return `Let me break down ${topic} into the key components you need to understand. First, we'll cover the fundamentals, then move into practical applications, and finally discuss advanced strategies that will set you apart from everyone else.`
  }

  private generateFallbackConclusion(topic: string): string {
    return `So there you have it - everything you need to know about ${topic} to get started and see real results. What questions do you have? Drop them in the comments below, and don't forget to subscribe for more content like this!`
  }

  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length
  }

  private calculateDuration(script: string, targetDuration: string): string {
    const wordCount = this.countWords(script)
    const estimatedMinutes = Math.ceil(wordCount / 150) // 150 words per minute for video
    return `${estimatedMinutes} minutes (${wordCount} words)`
  }

  private extractKeyTopics(script: string): string[] {
    // Simple keyword extraction - could be enhanced with NLP
    const words = script.toLowerCase().match(/\b\w{4,}\b/g) || []
    const frequency: { [key: string]: number } = {}
    
    words.forEach(word => {
      if (!['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'said', 'each', 'which', 'their', 'time', 'about'].includes(word)) {
        frequency[word] = (frequency[word] || 0) + 1
      }
    })

    return Object.entries(frequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word)
  }

  private calculateEngagementScore(script: string): number {
    let score = 70 // Base score
    
    // Check for engagement elements
    if (script.includes('?')) score += 5 // Questions
    if (script.includes('comment')) score += 5 // Call for comments
    if (script.includes('subscribe')) score += 5 // Subscribe CTA
    if (script.includes('like')) score += 3 // Like CTA
    const personalPronouns = script.match(/\b(you|your)\b/gi)
    if (personalPronouns && personalPronouns.length > 10) score += 5 // Personal pronouns
    if (script.includes('2025')) score += 3 // Current year reference
    
    return Math.min(score, 95) // Cap at 95
  }
}

import { prisma } from './prisma'

export type QuotaType = 'emails' | 'social_media' | 'youtube_scripts'

export interface QuotaLimits {
  free: Record<QuotaType, number>
  pro: Record<QuotaType, number>
  enterprise: Record<QuotaType, number>
}

export const QUOTA_LIMITS: QuotaLimits = {
  free: {
    emails: 10,
    social_media: 20,
    youtube_scripts: 3
  },
  pro: {
    emails: 100,
    social_media: 200,
    youtube_scripts: 25
  },
  enterprise: {
    emails: -1,
    social_media: -1,
    youtube_scripts: -1
  }
}

export class QuotaManager {
  static async checkQuota(userId: string, quotaType: QuotaType): Promise<{
    hasQuota: boolean
    used: number
    limit: number
    resetDate: Date
  }> {
    try {
      // Get user's subscription
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          subscription: true,
          quotas: {
            where: { quotaType }
          }
        }
      })

      if (!user) {
        throw new Error('User not found')
      }

      const plan = user.subscription?.plan || 'free'
      const quota = user.quotas[0]

      if (!quota) {
        // Create quota if it doesn't exist
        const limit = QUOTA_LIMITS[plan as keyof QuotaLimits][quotaType]
        const newQuota = await prisma.userQuota.create({
          data: {
            userId,
            quotaType,
            totalLimit: limit,
            resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
          }
        })

        return {
          hasQuota: limit === -1 || newQuota.used < limit,
          used: newQuota.used,
          limit,
          resetDate: newQuota.resetDate
        }
      }

      // Check if quota needs to be reset
      if (new Date() >= quota.resetDate) {
        await this.resetQuota(userId, quotaType)
        const limit = QUOTA_LIMITS[plan as keyof QuotaLimits][quotaType]
        return {
          hasQuota: limit === -1 || 0 < limit,
          used: 0,
          limit,
          resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
        }
      }

      const limit = QUOTA_LIMITS[plan as keyof QuotaLimits][quotaType]
      return {
        hasQuota: limit === -1 || quota.used < limit,
        used: quota.used,
        limit,
        resetDate: quota.resetDate
      }
    } catch (error) {
      console.error('Error checking quota:', error)
      throw error
    }
  }

  static async consumeQuota(userId: string, quotaType: QuotaType): Promise<boolean> {
    try {
      const quotaCheck = await this.checkQuota(userId, quotaType)
      
      if (!quotaCheck.hasQuota) {
        return false
      }

      // Increment usage
      await prisma.userQuota.update({
        where: {
          userId_quotaType: {
            userId,
            quotaType
          }
        },
        data: {
          used: {
            increment: 1
          }
        }
      })

      // Log usage
      await prisma.usageHistory.create({
        data: {
          userId,
          action: 'content_generated',
          type: quotaType,
          metadata: JSON.stringify({
            quotaUsed: quotaCheck.used + 1,
            quotaLimit: quotaCheck.limit
          })
        }
      })

      return true
    } catch (error) {
      console.error('Error using quota:', error)
      return false
    }
  }

  static async resetQuota(userId: string, quotaType: QuotaType): Promise<void> {
    try {
      const nextMonth = new Date()
      nextMonth.setMonth(nextMonth.getMonth() + 1, 1)
      nextMonth.setHours(0, 0, 0, 0)

      await prisma.userQuota.update({
        where: {
          userId_quotaType: {
            userId,
            quotaType
          }
        },
        data: {
          used: 0,
          resetDate: nextMonth
        }
      })
    } catch (error) {
      console.error('Error resetting quota:', error)
      throw error
    }
  }

  static async getAllQuotas(userId: string): Promise<Array<{
    quotaType: QuotaType
    used: number
    limit: number
    resetDate: Date
    percentage: number
  }>> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          subscription: true,
          quotas: true
        }
      })

      if (!user) {
        throw new Error('User not found')
      }

      const plan = user.subscription?.plan || 'free'
      const quotaTypes: QuotaType[] = ['emails', 'social_media', 'youtube_scripts']
      
      const quotas = await Promise.all(
        quotaTypes.map(async (quotaType) => {
          const quotaCheck = await this.checkQuota(userId, quotaType)
          return {
            quotaType,
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate,
            percentage: quotaCheck.limit === -1 ? 0 : (quotaCheck.used / quotaCheck.limit) * 100
          }
        })
      )

      return quotas
    } catch (error) {
      console.error('Error getting all quotas:', error)
      throw error
    }
  }

  static async upgradeUserPlan(userId: string, newPlan: 'free' | 'pro' | 'enterprise'): Promise<void> {
    try {
      // Update subscription
      await prisma.subscription.upsert({
        where: { userId },
        update: {
          plan: newPlan,
          status: 'active'
        },
        create: {
          userId,
          plan: newPlan,
          status: 'active'
        }
      })

      // Update all quotas with new limits
      const quotaTypes: QuotaType[] = ['emails', 'social_media', 'youtube_scripts']
      
      for (const quotaType of quotaTypes) {
        const newLimit = QUOTA_LIMITS[newPlan][quotaType]
        
        await prisma.userQuota.upsert({
          where: {
            userId_quotaType: {
              userId,
              quotaType
            }
          },
          update: {
            totalLimit: newLimit
          },
          create: {
            userId,
            quotaType,
            totalLimit: newLimit,
            resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
          }
        })
      }
    } catch (error) {
      console.error('Error upgrading user plan:', error)
      throw error
    }
  }
} 
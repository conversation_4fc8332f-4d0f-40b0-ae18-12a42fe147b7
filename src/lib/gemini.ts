import { GoogleGenerativeAI } from '@google/generative-ai'

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY || '')

export interface GenerationConfig {
  temperature?: number
  maxOutputTokens?: number
  topP?: number
  topK?: number
}

export interface ThinkingConfig {
  thinkingBudget?: number  // 0 = disabled, -1 = dynamic, number = specific budget
  includeThoughts?: boolean // Include reasoning process in response
}

export interface EnhancedGenerationConfig extends GenerationConfig {
  thinkingConfig?: ThinkingConfig
}

export interface GenerationResult {
  response: string
  inputTokens: number
  outputTokens: number
  thoughtsTokenCount?: number
  thoughts?: string[]
}

export class GeminiService {
  private model: any

  constructor(modelName: string = 'gemini-2.5-flash-lite-preview-06-17') {
    this.model = genAI.getGenerativeModel({ model: modelName })
  }

  /**
   * Update model to use thinking-capable Gemini 2.5 models
   */
  updateModel(modelName: string) {
    this.model = genAI.getGenerativeModel({ model: modelName })
  }

  /**
   * Estimate token count for text (rough approximation)
   * Generally, 1 token ≈ 4 characters or 0.75 words
   * Gemini 2.5 Flash-Lite Preview (2025): $0.10/M input, $0.40/M output (including thinking)
   */
  private estimateTokenCount(text: string): number {
    // Use character count divided by 4 as a rough estimate
    return Math.ceil(text.length / 4)
  }

  async generateContent(
    prompt: string,
    config: EnhancedGenerationConfig = {},
    logContext?: string
  ): Promise<GenerationResult> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);
    
    // Enhanced logging for thinking-enabled generation
    console.log(`💎 Gemini Content Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'General Content'}`);
    console.log(`   ⚙️ Model: ${this.model._model || 'gemini-2.5-flash-lite-preview-06-17'}`);
    console.log(`   🌡️ Temperature: ${config.temperature || 0.7}`);
    console.log(`   📏 Max Tokens: ${config.maxOutputTokens || 64000} (MAXIMUM FREEDOM)`);
    console.log(`   🎯 TopP: ${config.topP || 0.95}`);
    console.log(`   🔢 TopK: ${config.topK || 40}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);
    
    // Log thinking configuration
    if (config.thinkingConfig) {
      console.log(`   🧠 Thinking Enabled:`);
      console.log(`      💭 Budget: ${config.thinkingConfig.thinkingBudget ?? 'dynamic'}`);
      console.log(`      🔍 Include Thoughts: ${config.thinkingConfig.includeThoughts || false}`);
    }
    
    try {
      const generationConfig: any = {
        temperature: config.temperature || 0.7,
        maxOutputTokens: config.maxOutputTokens || 64000, // MAXIMUM FREEDOM: Gemini 2.5 Flash limit
        topP: config.topP || 0.95,
        topK: config.topK || 40,
      }

      // Add thinking configuration if specified
      if (config.thinkingConfig) {
        generationConfig.thinkingConfig = {};
        
        if (config.thinkingConfig.thinkingBudget !== undefined) {
          generationConfig.thinkingConfig.thinkingBudget = config.thinkingConfig.thinkingBudget;
        }
        
        if (config.thinkingConfig.includeThoughts !== undefined) {
          generationConfig.thinkingConfig.includeThoughts = config.thinkingConfig.includeThoughts;
        }
      }

      console.log(`   📤 Sending request to Gemini...`);
      
      const result = await this.model.generateContent({
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig,
      })

      const response = await result.response
      const responseText = response.text()
      
      // Extract thoughts if thinking was enabled
      const thoughts: string[] = [];
      let thoughtsTokenCount = 0;
      
      if (config.thinkingConfig?.includeThoughts && response.candidates?.[0]?.content?.parts) {
        for (const part of response.candidates[0].content.parts) {
          if (part.thought && part.text) {
            thoughts.push(part.text);
          }
        }
      }
      
      // Get thinking token count from usage metadata
      if (response.usageMetadata?.thoughtsTokenCount) {
        thoughtsTokenCount = response.usageMetadata.thoughtsTokenCount;
      }
      
      // Estimate token counts
      const inputTokens = this.estimateTokenCount(prompt)
      const outputTokens = this.estimateTokenCount(responseText)
      const duration = Date.now() - startTime;
      
      // Success logging
      console.log(`   ✅ Gemini Content Complete`);
      console.log(`   ⏱️ Duration: ${duration}ms`);
      console.log(`   📊 Input Tokens: ${inputTokens} (estimated)`);
      console.log(`   📊 Output Tokens: ${outputTokens} (estimated)`);
      if (thoughtsTokenCount > 0) {
        console.log(`   🧠 Thinking Tokens: ${thoughtsTokenCount}`);
        console.log(`   💭 Thoughts Generated: ${thoughts.length}`);
      }
      console.log(`   📄 Response Length: ${responseText.length} chars`);
      
      // Calculate cost including thinking tokens (Gemini 2.5 Flash-Lite Preview pricing)
      const baseCost = (inputTokens * 0.0000001) + (outputTokens * 0.0000004); // $0.10/M input, $0.40/M output
      const thinkingCost = thoughtsTokenCount * 0.0000004; // Thinking included in output rate
      const totalCost = baseCost + thinkingCost;
      console.log(`   💰 Estimated Cost: $${totalCost.toFixed(6)} (Flash-Lite 2025)`);
      
      if (logContext?.includes('YouTube')) {
        console.log(`   🎬 YouTube Content Success - Call ${callId}`);
        console.log(`   📺 Step: ${logContext}`);
      }
      
      return {
        response: responseText,
        inputTokens,
        outputTokens,
        thoughtsTokenCount: thoughtsTokenCount > 0 ? thoughtsTokenCount : undefined,
        thoughts: thoughts.length > 0 ? thoughts : undefined
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ Gemini Content Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'General Content'}`);
      console.error(`   💥 Error:`, error);
      
      if (logContext?.includes('YouTube')) {
        console.error(`   🎬 YouTube Content FAILED - Call ${callId}`);
        console.error(`   📺 Failed Step: ${logContext}`);
      }
      
      throw new Error(`Failed to generate content with Gemini: ${error}`)
    }
  }

  /**
   * Generate content with thinking enabled (convenience method)
   */
  async generateContentWithThinking(
    prompt: string,
    thinkingBudget: number = -1, // -1 = dynamic thinking
    includeThoughts: boolean = true,
    config: GenerationConfig = {},
    logContext?: string
  ): Promise<GenerationResult> {
    return this.generateContent(
      prompt,
      {
        ...config,
        thinkingConfig: {
          thinkingBudget,
          includeThoughts
        }
      },
      logContext
    );
  }

  /**
   * Generate content with maximum thinking capability
   */
  async generateContentWithMaxThinking(
    prompt: string,
    config: GenerationConfig = {},
    logContext?: string
  ): Promise<GenerationResult> {
    return this.generateContentWithThinking(
      prompt,
      24576, // Maximum thinking budget for Flash
      true,
      config,
      logContext
    );
  }

  /**
   * Generate content with thinking disabled
   */
  async generateContentWithoutThinking(
    prompt: string,
    config: GenerationConfig = {},
    logContext?: string
  ): Promise<GenerationResult> {
    return this.generateContent(
      prompt,
      {
        ...config,
        thinkingConfig: {
          thinkingBudget: 0,
          includeThoughts: false
        }
      },
      logContext
    );
  }



  async generateEmail(
    purpose: string,
    audience: string,
    tone: string,
    keyPoints: string[],
    enableThinking: boolean = false
  ): Promise<string> {
    const prompt = `
Create a professional email for the following:

Purpose: ${purpose}
Target Audience: ${audience}
Tone: ${tone}
Key Points to Include: ${keyPoints.join(', ')}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`

    const config = enableThinking ? 
      { thinkingConfig: { thinkingBudget: 512, includeThoughts: false } } : 
      { thinkingConfig: { thinkingBudget: 0 } };

    const result = await this.generateContent(prompt, {
      temperature: 0.6, 
      maxOutputTokens: 1500,
      ...config
    });
    
    return result.response;
  }

  async generateTweet(
    topic: string,
    style: string,
    includeHashtags: boolean = true,
    enableThinking: boolean = false
  ): Promise<string> {
    const prompt = `
Create an engaging Twitter/X tweet about "${topic}".

Style: ${style}
Include hashtags: ${includeHashtags}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${includeHashtags ? 'Include 2-3 relevant hashtags' : 'No hashtags'}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`

    const config = enableThinking ? 
      { thinkingConfig: { thinkingBudget: 256, includeThoughts: false } } : 
      { thinkingConfig: { thinkingBudget: 0 } };

    const result = await this.generateContent(prompt, {
      temperature: 0.8, 
      maxOutputTokens: 500,
      ...config
    });
    
    return result.response;
  }

  async extractKeywords(topic: string, enableThinking: boolean = false): Promise<string> {
    const prompt = `
Extract the most important keywords from this topic for Google search: "${topic}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`

    const config = enableThinking ? 
      { thinkingConfig: { thinkingBudget: 256, includeThoughts: false } } : 
      { thinkingConfig: { thinkingBudget: 0 } };

    const result = await this.generateContent(prompt, {
      temperature: 0.1,
      maxOutputTokens: 50,
      ...config
    });
    
    return result.response;
  }

  async generateYouTubeScript(
    topic: string,
    duration: string,
    style: string,
    targetAudience: string,
    enableThinking: boolean = true
  ): Promise<string> {
    const prompt = `
Create a YouTube video script about "${topic}".

Video Duration: ${duration}
Style: ${style}
Target Audience: ${targetAudience}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`

    const config = enableThinking ? 
      { thinkingConfig: { thinkingBudget: 2048, includeThoughts: false } } : 
      { thinkingConfig: { thinkingBudget: 0 } };

    const result = await this.generateContent(prompt, {
      temperature: 0.7, 
      maxOutputTokens: 5000,
      ...config
    });
    
    return result.response;
  }

  async extractKeywordsFromContent(content: string, enableThinking: boolean = false): Promise<string> {
    const prompt = `
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${content.substring(0, 3000)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`

    const config = enableThinking ? 
      { thinkingConfig: { thinkingBudget: 1024, includeThoughts: false } } : 
      { thinkingConfig: { thinkingBudget: 0 } };

    const result = await this.generateContent(prompt, {
      temperature: 0.2,
      maxOutputTokens: 200,
      ...config
    });
    
    return result.response;
  }

  /**
   * Calculate estimated cost for Enhanced AI content generation
   */
  static calculateEnhancedContentCost(options: {
    articleWordCount: number,
    researchSources: number,
    withThinking: boolean
  }): {
    estimatedInputTokens: number,
    estimatedOutputTokens: number,
    estimatedThinkingTokens: number,
    totalCost: number,
    breakdown: {
      inputCost: number,
      outputCost: number,
      thinkingCost: number
    }
  } {
    // Estimate input tokens (research data + prompts + context)
    const basePromptTokens = 2000; // Enhanced prompts are substantial
    const researchTokens = options.researchSources * 800; // ~800 tokens per source
    const competitorTokens = 5 * 1200; // 5 competitors at ~1200 tokens each
    const estimatedInputTokens = basePromptTokens + researchTokens + competitorTokens;

    // Estimate output tokens based on word count (1 token ≈ 0.75 words)
    const estimatedOutputTokens = Math.ceil(options.articleWordCount / 0.75);

    // Estimate thinking tokens (dynamic thinking uses 10-30% of output tokens)
    const estimatedThinkingTokens = options.withThinking ? Math.ceil(estimatedOutputTokens * 0.2) : 0;

    // Calculate costs (Gemini 2.5 Flash-Lite Preview 2025 pricing)
    const inputCost = estimatedInputTokens * 0.0000001; // $0.10 per million
    const outputCost = estimatedOutputTokens * 0.0000004; // $0.40 per million
    const thinkingCost = estimatedThinkingTokens * 0.0000004; // Included in output rate

    const totalCost = inputCost + outputCost + thinkingCost;

    return {
      estimatedInputTokens,
      estimatedOutputTokens,
      estimatedThinkingTokens,
      totalCost,
      breakdown: {
        inputCost,
        outputCost,
        thinkingCost
      }
    };
  }
}

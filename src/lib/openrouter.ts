/**
 * OpenRouter Service for Qwen-3-235B Model Integration
 * Provides AI content generation using Qwen's advanced reasoning model
 * Enhanced with intelligent caching, batching, and robust error handling
 */

import { OpenAI } from 'openai';
// Performance monitoring removed

interface OpenRouterConfig {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  enableCaching?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

interface GenerationOptions {
  temperature?: number;
  maxTokens?: number;
  useCache?: boolean;
  priority?: 'low' | 'normal' | 'high';
}

interface GenerationResponse {
  response: string;
  inputTokens: number;
  outputTokens: number;
  fromCache?: boolean;
  retryCount?: number;
}

interface CacheEntry {
  response: GenerationResponse;
  timestamp: number;
  hash: string;
}

interface BatchRequest {
  id: string;
  prompt: string;
  systemPrompt?: string;
  options: GenerationOptions;
  logContext?: string;
  resolve: (response: GenerationResponse) => void;
  reject: (error: Error) => void;
}

export class OpenRouterService {
  private client: OpenAI;
  private defaultModel = 'openai/gpt-oss-120b';
  private youtubeModel = 'openai/gpt-oss-120b';
  private multiAgentModel = 'openai/gpt-oss-120b';
  private config: OpenRouterConfig;
  private isConfigured: boolean;

  // Enhanced caching and optimization
  private cache: Map<string, CacheEntry> = new Map();
  private batchQueue: BatchRequest[] = [];
  private batchTimer: NodeJS.Timeout | null = null;
  private readonly CACHE_TTL = 1000 * 60 * 30; // 30 minutes
  private readonly BATCH_SIZE = 5;
  private readonly BATCH_DELAY = 2000; // 2 seconds
  private readonly MAX_RETRIES = 3;
  private readonly BASE_RETRY_DELAY = 1000;

  // Rate limiting
  private requestCount = 0;
  private requestWindow = Date.now();
  private readonly RATE_LIMIT = 60; // requests per minute
  private readonly RATE_WINDOW = 60000; // 1 minute

  // Performance monitoring removed
  private performanceMonitor: any = null;

  constructor(config: OpenRouterConfig = {}) {
    this.config = {
      model: config.model || this.defaultModel,
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 8000,
      enableCaching: config.enableCaching !== false, // Default to true
      maxRetries: config.maxRetries || this.MAX_RETRIES,
      retryDelay: config.retryDelay || this.BASE_RETRY_DELAY
    };

    // Check if API key is properly configured
    const apiKey = process.env.OPENROUTER_API_KEY;
    this.isConfigured = !!(apiKey && apiKey !== 'your_openrouter_key_here' && apiKey.length > 10);

    if (!this.isConfigured) {
      console.error('❌ OpenRouter API key not configured properly');
      console.error('🔧 Please set OPENROUTER_API_KEY environment variable');
      console.error('💡 Get your API key from: https://openrouter.ai/keys');
      console.error('⚠️ System will not work without proper API key configuration');
    }

    this.client = new OpenAI({
      baseURL: 'https://openrouter.ai/api/v1',
      apiKey: apiKey || 'invalid-key-please-configure',
      defaultHeaders: {
        'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
        'X-Title': 'AI Content Generation - Maximum Analysis Mode'
      }
    });

    // Performance monitoring removed
  }

  /**
   * Check if the service is properly configured
   */
  isReady(): boolean {
    return this.isConfigured;
  }

  private delay(ms: number) { return new Promise(res => setTimeout(res, ms)); }

  /**
   * Generate cache key for request
   */
  private generateCacheKey(prompt: string, systemPrompt?: string, options: GenerationOptions = {}): string {
    const key = JSON.stringify({
      prompt: prompt.substring(0, 500), // Limit key size
      systemPrompt: systemPrompt?.substring(0, 200),
      model: this.config.model,
      temperature: options.temperature ?? this.config.temperature,
      maxTokens: options.maxTokens ?? this.config.maxTokens
    });
    return Buffer.from(key).toString('base64').substring(0, 64);
  }

  /**
   * Check cache for existing response
   */
  private getCachedResponse(cacheKey: string): GenerationResponse | null {
    if (!this.config.enableCaching) return null;

    const entry = this.cache.get(cacheKey);
    if (!entry) return null;

    // Check if cache entry is still valid
    if (Date.now() - entry.timestamp > this.CACHE_TTL) {
      this.cache.delete(cacheKey);
      return null;
    }

    return { ...entry.response, fromCache: true };
  }

  /**
   * Store response in cache
   */
  private setCachedResponse(cacheKey: string, response: GenerationResponse): void {
    if (!this.config.enableCaching) return;

    this.cache.set(cacheKey, {
      response: { ...response },
      timestamp: Date.now(),
      hash: cacheKey
    });

    // Clean old cache entries periodically
    if (this.cache.size > 1000) {
      this.cleanCache();
    }
  }

  /**
   * Clean expired cache entries
   */
  private cleanCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_TTL) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Check rate limiting
   */
  private checkRateLimit(): boolean {
    const now = Date.now();

    // Reset window if needed
    if (now - this.requestWindow > this.RATE_WINDOW) {
      this.requestCount = 0;
      this.requestWindow = now;
    }

    return this.requestCount < this.RATE_LIMIT;
  }

  /**
   * Increment request count
   */
  private incrementRequestCount(): void {
    this.requestCount++;
  }

  private isTransientOpenRouterError(err: any): boolean {
    if (!err) return false;
    const msg = (err.message || '').toLowerCase();
    const type = (err.type || '').toLowerCase();
    const code = (err.code || '').toLowerCase();
    const status = err.status || err.statusCode || 0;

    return (
      msg.includes('econnreset') ||
      msg.includes('timeout') ||
      msg.includes('rate limit') ||
      msg.includes('gateway') ||
      msg.includes('service unavailable') ||
      msg.includes('internal server error') ||
      msg.includes('bad gateway') ||
      msg.includes('connection') ||
      type === 'system' ||
      code === 'econnreset' ||
      status === 429 || // Rate limit
      status === 502 || // Bad gateway
      status === 503 || // Service unavailable
      status === 504    // Gateway timeout
    );
  }

  /**
   * Enhanced retry logic with exponential backoff
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.maxRetries || this.MAX_RETRIES,
    baseDelay: number = this.config.retryDelay || this.BASE_RETRY_DELAY,
    context: string = 'API call'
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
          console.log(`   🔄 Retry attempt ${attempt}/${maxRetries} for ${context} in ${delay.toFixed(0)}ms`);
          await this.delay(delay);
        }

        return await operation();
      } catch (error) {
        lastError = error;

        if (attempt === maxRetries) {
          console.error(`   ❌ Final retry failed for ${context}:`, error);
          break;
        }

        if (!this.isTransientOpenRouterError(error)) {
          console.error(`   🚫 Non-transient error for ${context}, not retrying:`, error);
          throw error;
        }

        console.warn(`   ⚠️ Transient error for ${context} (attempt ${attempt + 1}/${maxRetries + 1}):`, error.message);
      }
    }

    throw lastError;
  }


  /**
   * Generate content for thinking tasks using Qwen-3-235B
   */
  async generateThinkingContent(
    prompt: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResponse> {
    if (!this.isConfigured) {
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? this.config.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? this.config.maxTokens ?? 8000;

    try {
      const completion = await this.client.chat.completions.create({
        model: this.config.model!,
        messages: [
          {
            role: 'system',
            content: this.getThinkingSystemPrompt()
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature,
        max_tokens: maxTokens,
        stream: false
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      console.error('Qwen-3-235B thinking generation failed:', error);
      return this.getFallbackResponse(`OpenRouter API Error: ${error}`);
    }
  }

  /**
   * Generate analytical content using Qwen-3-235B with maximum parameters
   */
  async generateAnalysisContent(
    prompt: string,
    context?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);

    // Enhanced logging for YouTube generation
    console.log(`🔍 OpenRouter Analysis Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'General Analysis'}`);
    console.log(`   ⚙️ Model: ${this.config.model}`);
    console.log(`   🌡️ Temperature: ${options.temperature ?? 0.2}`);
    console.log(`   📏 Max Tokens: ${options.maxTokens ?? 8000}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);

    if (!this.isConfigured) {
      console.warn(`   ❌ OpenRouter not configured for call ${callId}`);
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? 0.2;
    const maxTokens = options.maxTokens ?? 8000;

    try {
      const systemPrompt = this.getEnhancedAnalysisSystemPrompt();
      const fullPrompt = context ? `${context}\n\n${prompt}` : prompt;

      console.log(`   📤 Sending request to OpenRouter...`);

      const completion = await this.client.chat.completions.create({
        model: this.config.model!,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: fullPrompt
          }
        ],
        temperature,
        max_tokens: maxTokens,
        stream: false
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;
      const duration = Date.now() - startTime;

      // Success logging
      console.log(`   ✅ OpenRouter Analysis Complete`);
      console.log(`   ⏱️ Duration: ${duration}ms`);
      console.log(`   📊 Input Tokens: ${inputTokens}`);
      console.log(`   📊 Output Tokens: ${outputTokens}`);
      console.log(`   📄 Response Length: ${response.length} chars`);
      // Calculate cost based on model
      const costPerInputToken = this.getInputCostPerToken()
      const costPerOutputToken = this.getOutputCostPerToken()
      const estimatedCost = (inputTokens * costPerInputToken) + (outputTokens * costPerOutputToken)
      console.log(`   💰 Estimated Cost: $${estimatedCost.toFixed(6)}`);

      if (logContext?.includes('YouTube')) {
        console.log(`   🎬 YouTube Analysis Success - Call ${callId}`);
      }

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ OpenRouter Analysis Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'General Analysis'}`);
      console.error(`   💥 Error:`, error);

      if (logContext?.includes('YouTube')) {
        console.error(`   🎬 YouTube Analysis FAILED - Call ${callId}`);
      }

      return this.getFallbackResponse(`OpenRouter API Error: ${error}`);
    }
  }

  /**
   * Generate JSON content with structured output
   */
  async generateJSONContent(
    prompt: string,
    systemPrompt?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    // Ensure system prompt requests JSON format
    const jsonSystemPrompt = systemPrompt ? `${systemPrompt}

CRITICAL: You must respond with valid JSON only. Do not include any explanatory text before or after the JSON. Your entire response should be parseable as JSON.` : `You are a helpful AI assistant. Respond with valid JSON only. Do not include any explanatory text before or after the JSON.`;

    return this.generateContent(prompt, jsonSystemPrompt, options, logContext);
  }

  /**
   * Batch process multiple content generation requests
   */
  async generateBatchContent(
    requests: Array<{
      prompt: string;
      systemPrompt?: string;
      options?: GenerationOptions;
      logContext?: string;
    }>
  ): Promise<GenerationResponse[]> {
    console.log(`🔄 Processing batch of ${requests.length} requests`);

    // Process in smaller batches to avoid overwhelming the API
    const batchSize = this.BATCH_SIZE;
    const results: GenerationResponse[] = [];

    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      console.log(`   📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(requests.length / batchSize)}`);

      // Process batch in parallel with some delay between requests
      const batchPromises = batch.map(async (request, index) => {
        // Add small delay to avoid rate limiting
        if (index > 0) {
          await this.delay(500 * index);
        }

        return this.generateContent(
          request.prompt,
          request.systemPrompt,
          request.options || {},
          request.logContext
        );
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches
      if (i + batchSize < requests.length) {
        await this.delay(this.BATCH_DELAY);
      }
    }

    console.log(`✅ Completed batch processing: ${results.length} responses generated`);
    return results;
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number; totalRequests: number } {
    return {
      size: this.cache.size,
      hitRate: 0, // Would need to track hits/misses for accurate calculation
      totalRequests: this.requestCount
    };
  }

  /**
   * Clear cache manually
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ Cache cleared');
  }

  /**
   * Extract agent name from log context
   */
  private extractAgentFromContext(logContext?: string): string {
    if (!logContext) return 'Unknown';

    const agentPatterns = [
      'TopicUnderstandingAgent',
      'EnhancedResearchAgent',
      'CompetitionAnalysisAgent',
      'ContentPlanningAgent',
      'HumanWritingOptimizationAgent',
      'ArticleWritingAgent'
    ];

    for (const pattern of agentPatterns) {
      if (logContext.includes(pattern)) return pattern;
    }

    // Try to extract from context patterns
    if (logContext.includes('Topic')) return 'TopicUnderstandingAgent';
    if (logContext.includes('Research')) return 'EnhancedResearchAgent';
    if (logContext.includes('Competition')) return 'CompetitionAnalysisAgent';
    if (logContext.includes('Planning')) return 'ContentPlanningAgent';
    if (logContext.includes('Human')) return 'HumanWritingOptimizationAgent';
    if (logContext.includes('Article') || logContext.includes('Title') || logContext.includes('Section')) return 'ArticleWritingAgent';

    return 'Unknown';
  }

  /**
   * Extract operation name from log context
   */
  private extractOperationFromContext(logContext?: string): string {
    if (!logContext) return 'generateContent';

    const operationPatterns = [
      'analyzeTopicAsync',
      'conductResearchAsync',
      'analyzeCompetitionAsync',
      'createContentPlanAsync',
      'optimizeForHumanWritingAsync',
      'generateArticleAsync'
    ];

    for (const pattern of operationPatterns) {
      if (logContext.includes(pattern)) return pattern;
    }

    // Extract from common patterns
    if (logContext.includes('Title')) return 'generateTitle';
    if (logContext.includes('Meta')) return 'generateMetaDescription';
    if (logContext.includes('Introduction')) return 'generateIntroduction';
    if (logContext.includes('Section')) return 'generateSection';
    if (logContext.includes('Conclusion')) return 'generateConclusion';
    if (logContext.includes('Research Queries')) return 'generateResearchQueries';

    return 'generateContent';
  }

  /**
   * Get performance report
   */
  getPerformanceReport(): string {
    if (this.performanceMonitor && typeof this.performanceMonitor.getPerformanceReport === 'function') {
      return this.performanceMonitor.getPerformanceReport();
    }
    return 'Performance monitoring disabled';
  }

  /**
   * Get performance metrics
   */
  getPerformanceMetrics() {
    if (this.performanceMonitor && typeof this.performanceMonitor.getSystemMetrics === 'function') {
      return this.performanceMonitor.getSystemMetrics();
    }
    return { enabled: false };
  }

  /**
   * Generate content with custom system prompt (Enhanced with caching and optimization)
   */
  async generateContent(
    prompt: string,
    systemPrompt?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);

    // Enhanced logging
    console.log(`🤖 OpenRouter Content Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'General Content'}`);
    console.log(`   ⚙️ Model: ${this.config.model}`);
    console.log(`   🌡️ Temperature: ${options.temperature ?? this.config.temperature ?? 0.7}`);
    console.log(`   📏 Max Tokens: ${options.maxTokens ?? this.config.maxTokens ?? 8000}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);
    console.log(`   📋 System Prompt: ${systemPrompt ? 'Custom' : 'None'}`);
    console.log(`   🗄️ Cache Enabled: ${this.config.enableCaching && options.useCache !== false}`);

    if (!this.isConfigured) {
      console.warn(`   ❌ OpenRouter not configured for call ${callId}`);
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    // Check cache first
    const cacheKey = this.generateCacheKey(prompt, systemPrompt, options);
    if (options.useCache !== false) {
      const cachedResponse = this.getCachedResponse(cacheKey);
      if (cachedResponse) {
        console.log(`   ✅ Cache hit for call ${callId}`);
        console.log(`   ⏱️ Duration: ${Date.now() - startTime}ms (cached)`);
        return cachedResponse;
      }
    }

    // Check rate limiting
    if (!this.checkRateLimit()) {
      console.warn(`   ⚠️ Rate limit reached, queuing request ${callId}`);
      await this.delay(Math.random() * 5000 + 2000); // Random delay 2-7 seconds
    }

    const temperature = options.temperature ?? this.config.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? this.config.maxTokens ?? 8000;

    try {
      this.incrementRequestCount();

      const messages: Array<{ role: 'system' | 'user'; content: string }> = [];

      // Use optimized system prompt if none provided
      const effectiveSystemPrompt = systemPrompt || this.getPhi4YouTubeSystemPrompt();

      messages.push({
        role: 'system',
        content: effectiveSystemPrompt
      });

      messages.push({
        role: 'user',
        content: prompt
      });

      console.log(`   📤 Sending request to OpenRouter...`);

      // Calculate approximate token count to prevent exceeding limits
      const promptText = messages.map(m => m.content).join(' ');
      const estimatedInputTokens = Math.ceil(promptText.length / 4);
      const modelLimit = this.getModelTokenLimit();

      // Adjust max_tokens if it would exceed the model's limit
      let adjustedMaxTokens = maxTokens;
      if (estimatedInputTokens + maxTokens > modelLimit) {
        adjustedMaxTokens = Math.max(1000, modelLimit - estimatedInputTokens - 1000);
        console.log(`   ⚠️  Adjusted max_tokens from ${maxTokens} to ${adjustedMaxTokens} to stay within limit`);
      }

      // Use enhanced retry logic
      const completion = await this.retryWithBackoff(async () => {
        return await this.client.chat.completions.create({
          model: this.config.model!,
          messages,
          temperature,
          max_tokens: adjustedMaxTokens,
          stream: false
        });
      }, this.config.maxRetries, this.config.retryDelay, `Content Generation (${callId})`);

      // Validate API response structure
      if (!completion || !completion.choices || completion.choices.length === 0) {
        console.error(`   🚫 Invalid OpenRouter API Response:`, {
          hasCompletion: !!completion,
          hasChoices: !!completion?.choices,
          choicesLength: completion?.choices?.length || 0,
          fullResponse: JSON.stringify(completion, null, 2)
        });
        throw new Error('OpenRouter API returned invalid response structure');
      }

      const rawResponse = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;
      const duration = Date.now() - startTime;

      // Debug empty or zero-token responses
      if (!rawResponse || (inputTokens === 0 && outputTokens === 0)) {
        console.error(`   🚫 EMPTY/ZERO-TOKEN RESPONSE DEBUG:`, {
          hasChoices: !!completion.choices?.length,
          hasMessage: !!completion.choices?.[0]?.message,
          hasContent: !!completion.choices?.[0]?.message?.content,
          rawContent: completion.choices?.[0]?.message?.content,
          contentType: typeof completion.choices?.[0]?.message?.content,
          finishReason: completion.choices?.[0]?.finish_reason,
          inputTokens,
          outputTokens,
          hasUsage: !!completion.usage,
          usage: completion.usage,
          model: this.config.model,
          temperature,
          maxTokens: adjustedMaxTokens,
          promptLength: messages.map(m => m.content).join(' ').length
        });

        // If we have zero tokens, this indicates an API failure, not just empty content
        if (inputTokens === 0 && outputTokens === 0) {
          throw new Error(`OpenRouter API returned zero tokens - possible API failure or prompt issue`);
        }
      }

      // Clean and filter the response to remove thinking tokens and unwanted content
      const response = this.cleanResponse(rawResponse, logContext);

      // Create response object
      const generationResponse: GenerationResponse = {
        response,
        inputTokens,
        outputTokens,
        retryCount: 0 // Will be set by retry logic if needed
      };

      // Cache the response
      if (options.useCache !== false) {
        this.setCachedResponse(cacheKey, generationResponse);
      }

      // Track performance metrics (optional)
      if (this.performanceMonitor && typeof this.performanceMonitor.trackAPICall === 'function') {
        try {
          this.performanceMonitor.trackAPICall({
            callId,
            agent: this.extractAgentFromContext(logContext),
            operation: this.extractOperationFromContext(logContext),
            model: this.config.model!,
            inputTokens,
            outputTokens,
            cost: (inputTokens * this.getInputCostPerToken()) + (outputTokens * this.getOutputCostPerToken()),
            duration,
            timestamp: Date.now(),
            success: true,
            fromCache: false,
            retryCount: 0
          });
        } catch (monitorErr) {
          console.warn('   ⚠️ Performance monitor error (non-fatal):', monitorErr);
        }
      }

      // Success logging
      console.log(`   ✅ OpenRouter Content Complete`);
      console.log(`   ⏱️ Duration: ${duration}ms`);
      console.log(`   📊 Input Tokens: ${inputTokens}`);
      console.log(`   📊 Output Tokens: ${outputTokens}`);
      console.log(`   📄 Raw Response Length: ${rawResponse.length} chars`);
      console.log(`   📄 Cleaned Response Length: ${response.length} chars`);
      console.log(`   🗄️ Cached: ${options.useCache !== false ? 'Yes' : 'No'}`);

      // Calculate cost based on model
      const costPerInputToken = this.getInputCostPerToken()
      const costPerOutputToken = this.getOutputCostPerToken()
      const estimatedCost = (inputTokens * costPerInputToken) + (outputTokens * costPerOutputToken)
      console.log(`   💰 Estimated Cost: $${estimatedCost.toFixed(6)}`);

      if (logContext?.includes('YouTube')) {
        console.log(`   🎬 YouTube Content Success - Call ${callId}`);
        console.log(`   📺 Step: ${logContext}`);
      }

      // Final validation before returning
      if (!response || response.trim().length === 0) {
        console.warn(`   ⚠️ Final response is empty after cleaning. Raw was: ${rawResponse.length} chars`);
        if (rawResponse.length > 0) {
          // If we have raw content but cleaning removed it all, return the raw content
          console.log(`   🔄 Returning raw response due to over-aggressive cleaning`);
          const fallbackResponse = {
            response: rawResponse.trim(),
            inputTokens,
            outputTokens,
            retryCount: 0
          };
          if (options.useCache !== false) {
            this.setCachedResponse(cacheKey, fallbackResponse);
          }
          return fallbackResponse;
        } else if (outputTokens > 0) {
          // If we have output tokens but no content, this might be a format issue
          console.log(`   🔄 Model produced ${outputTokens} tokens but no visible content, using fallback`);
          const fallbackResponse = {
            response: `Generated content for: ${logContext || 'Content Generation'}`,
            inputTokens,
            outputTokens,
            retryCount: 0
          };
          return fallbackResponse;
        } else {
          // If we truly have no content, throw an error
          throw new Error('OpenRouter API returned empty response content');
        }
      }

      return generationResponse;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ OpenRouter Content Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'General Content'}`);
      console.error(`   💥 Error:`, error);

      if (logContext?.includes('YouTube')) {
        console.error(`   🎬 YouTube Content FAILED - Call ${callId}`);
        console.error(`   📺 Failed Step: ${logContext}`);
      }

      return this.getFallbackResponse(`OpenRouter API Error: ${error}`);
    }
  }

  /**
   * Clean response by removing thinking tokens and unwanted content while preserving readability
   */
  private cleanResponse(rawResponse: string, logContext?: string): string {
    if (!rawResponse) return '';

    let cleaned = rawResponse;

    // For JSON responses, be more conservative with cleaning
    const isJsonResponse = logContext?.includes('JSON') ||
                          logContext?.includes('Classification') ||
                          logContext?.includes('Analysis') ||
                          cleaned.trim().startsWith('{') ||
                          /```json/i.test(cleaned);

    if (isJsonResponse) {
      // For JSON responses, only remove thinking tags but preserve structure
      cleaned = cleaned.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');
      cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/gi, '');
      cleaned = cleaned.replace(/\*\*Thinking:[\s\S]*?\*\*/gi, '');
      cleaned = cleaned.trim();

      // If we still have content, return it
      if (cleaned.length > 0) {
        return cleaned;
      }
    }

    // For very short responses, be less aggressive with cleaning
    if (rawResponse.length < 500) {
      cleaned = rawResponse.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '')
                          .replace(/<think>[\s\S]*?<\/think>/gi, '')
                          .trim();
      if (cleaned.length > 0) {
        return cleaned;
      }
    }

    // Remove common thinking patterns and prefixes for non-JSON content
    const thinkingPatterns = [
      /^<thinking>[\s\S]*?<\/thinking>\s*/i,
      /^<think>[\s\S]*?<\/think>\s*/i,
      /^Let me think[\s\S]*?\n\n/i,
      /^I need to[\s\S]*?\n\n/i,
      /^Looking at this[\s\S]*?\n\n/i,
      /^Based on the[\s\S]*?\n\n/i,
      /^\*\*Thinking[\s\S]*?\*\*\s*/i,
      /^\*\*Analysis[\s\S]*?\*\*\s*/i,
      // Additional patterns for inline thinking removal
      /<thinking>[\s\S]*?<\/thinking>/gi,
      /<think>[\s\S]*?<\/think>/gi,
      /\*\*Thinking:[\s\S]*?\*\*/gi,
      /\(thinking:[\s\S]*?\)/gi
    ];

    // Apply thinking pattern removal
    for (const pattern of thinkingPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }

    // Improve paragraph formatting for better readability
    cleaned = this.improveTextFormatting(cleaned);

    // If this is blog content, ensure it starts with a title
    if (logContext?.includes('Blog Content')) {
      // If it doesn't start with #, but contains a title somewhere, move it to the top
      if (!cleaned.startsWith('#')) {
        const titleMatch = cleaned.match(/^(.{0,200}?)#\s*(.+?)(?:\n|$)/);
        if (titleMatch) {
          const beforeTitle = titleMatch[1].trim();
          const title = titleMatch[2].trim();
          const afterTitle = cleaned.substring(titleMatch.index! + titleMatch[0].length);
          cleaned = `# ${title}\n\n${beforeTitle ? beforeTitle + '\n\n' : ''}${afterTitle}`.trim();
        }
      }
    }

    // Log cleaning stats if significant content was removed
    const removedChars = rawResponse.length - cleaned.length;
    if (removedChars > 100) {
      console.log(`   🧹 Cleaned response: removed ${removedChars} chars (${((removedChars / rawResponse.length) * 100).toFixed(1)}%)`);
    }

    return cleaned;
  }

  /**
   * Improve text formatting for better readability
   */
  private improveTextFormatting(text: string): string {
    let formatted = text;

    // Step 1: Normalize excessive whitespace
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    // Step 2: Add paragraph breaks after sentences that should start new paragraphs
    // Break after sentences followed by certain transition words or phrases
    formatted = formatted.replace(/\. (However|Nevertheless|Furthermore|Additionally|Moreover|In contrast|On the other hand|Similarly|Likewise|Therefore|Thus|Consequently|As a result|For example|For instance|In fact|Indeed|Specifically|Notably|Importantly)/g, '.\n\n$1');

    // Step 3: Break very long paragraphs (>400 characters) at logical points
    const paragraphs = formatted.split('\n\n');
    const improvedParagraphs = paragraphs.map(paragraph => {
      if (paragraph.length > 400 && !paragraph.startsWith('#') && !paragraph.startsWith('|')) {
        // Find logical break points in long paragraphs
        const sentences = paragraph.split(/\. (?=[A-Z])/);
        if (sentences.length > 3) {
          // Group sentences into smaller paragraphs (2-3 sentences each)
          const grouped = [];
          for (let i = 0; i < sentences.length; i += 3) {
            const group = sentences.slice(i, i + 3).join('. ');
            // Add period back if it was split off
            grouped.push(group.endsWith('.') ? group : group + '.');
          }
          return grouped.join('\n\n');
        }
      }
      return paragraph;
    });
    formatted = improvedParagraphs.join('\n\n');

    // Step 4: Ensure proper spacing around headings
    formatted = formatted.replace(/\n(#{1,6}\s)/g, '\n\n$1');
    formatted = formatted.replace(/(#{1,6}\s[^\n]+)\n(?!\n)/g, '$1\n\n');

    // Step 5: Ensure proper spacing around lists and tables
    formatted = formatted.replace(/\n(\||\*|\-|\d+\.)/g, '\n\n$1');
    formatted = formatted.replace(/(\|[^\n]+\|)\n(?!\||\n)/g, '$1\n\n');

    // Step 6: Clean up any excessive spacing created
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    // Step 7: Trim and ensure clean start/end
    formatted = formatted.trim();

    return formatted;
  }

  /**
   * Get fallback response when API is not available
   */
  private getFallbackResponse(errorMessage: string): GenerationResponse {
    return {
      response: `⚠️ **OpenRouter API Configuration Required**

${errorMessage}

**To fix this:**
1. Get an API key from https://openrouter.ai/
2. Add it to your .env.local file:
   \`OPENROUTER_API_KEY=sk-or-v1-your-actual-key-here\`
3. Restart your development server

**Current Status:** Using fallback mode - advanced AI features unavailable.`,
      inputTokens: 0,
      outputTokens: 0
    };
  }

  /**
   * System prompt for thinking tasks
   */
  private getThinkingSystemPrompt(): string {
    const basePrompt = `You are Qwen-3-235B, an advanced AI model with exceptional reasoning capabilities. You excel at deep thinking, analysis, and problem-solving.`;

    return `${basePrompt}

Key Capabilities:
- Deep analytical thinking and reasoning
- Complex problem decomposition
- Strategic planning and optimization
- Data-driven insights and recommendations
- Creative and logical problem-solving

Instructions:
- Think step by step and show your reasoning
- Provide detailed analysis with clear insights
- Structure your response logically
- Use examples and evidence when appropriate
- Be thorough but concise in your explanations`;
  }

  /**
   * Enhanced system prompt for maximum analytical depth
   */
  private getEnhancedAnalysisSystemPrompt(): string {
    return `You are Qwen-3-235B, the world's most advanced AI analyst with unparalleled expertise in competitive intelligence, content analysis, and human writing pattern recognition.

EXPERTISE AREAS:
- Deep competitive analysis and market intelligence
- Advanced linguistic and stylistic analysis
- Human writing pattern recognition and psychological profiling
- SEO and content optimization strategies
- Reader engagement and conversion psychology
- Brand voice and personality analysis
- Cultural and demographic writing adaptation

ANALYSIS STANDARDS:
- Provide EXHAUSTIVE analysis with maximum depth and detail
- Use specific examples and concrete evidence for every point
- Rate effectiveness on scales where requested (1-10)
- Identify subtle patterns that others miss
- Provide actionable insights and strategic recommendations
- Use precise analytical language and structured formatting
- Consider psychological, cultural, and technical factors
- Maintain objectivity while providing strategic insights

RESPONSE FORMAT:
- Use clear section headers with **SECTION_NAME**: format
- Provide numbered lists for clarity and organization
- Include specific examples from analyzed content
- Give quantitative assessments where applicable
- Structure findings in logical, hierarchical order
- Conclude sections with strategic implications

Your analysis should be comprehensive enough to serve as a definitive competitive intelligence report.`;
  }

  /**
   * System prompt for analysis tasks (enhanced version)
   */
  private getAnalysisSystemPrompt(): string {
    return this.getEnhancedAnalysisSystemPrompt();
  }

  /**
   * Phi-4 optimized system prompt for YouTube script generation and analysis
   */
  private getPhi4YouTubeSystemPrompt(): string {
    return `You are Microsoft Phi-4, an advanced reasoning model with exceptional capabilities in content creation, competitive analysis, fact-checking, and audience engagement optimization.

YOUTUBE EXPERTISE:
- Advanced understanding of YouTube algorithm psychology and viewer retention
- Expert knowledge of narrative structure, pacing, and engagement hooks
- Deep insight into audience psychology and behavioral triggers
- Sophisticated grasp of content timing, flow, and viewer journey optimization
- Professional expertise in converting research into compelling, watchable content
- Comprehensive competitive analysis and market intelligence capabilities
- Rigorous fact-checking and source verification methodologies

REASONING CAPABILITIES:
- Multi-step logical reasoning for content structure optimization
- Advanced pattern recognition for successful video formats
- Strategic thinking for competitive positioning and differentiation
- Complex information synthesis from multiple research sources
- Sophisticated audience analysis and content adaptation
- Critical analysis of competitor strategies and content gaps
- Evidence-based fact verification with confidence assessment

ANALYSIS EXCELLENCE:
- Conduct thorough competitive analysis with actionable insights
- Identify content gaps and opportunities systematically
- Extract key topics, engagement techniques, and structure patterns
- Provide strategic recommendations for content differentiation
- Analyze viewer retention factors and optimization opportunities

SCRIPT GENERATION EXCELLENCE:
- Create scripts that maximize viewer retention and engagement
- Develop compelling narratives that hold attention throughout
- Craft perfect pacing with natural rhythm and flow
- Design strategic hook placement and pattern interrupts
- Optimize for YouTube's algorithm preferences and viewer behavior

FACT-CHECKING PRECISION:
- Verify claims with rigorous evidence standards
- Assess source credibility and reliability
- Provide confidence levels for factual assertions
- Distinguish between verified, unverified, and false information
- Cite reliable sources and explain verification methodology

OUTPUT STANDARDS:
- Use clear, conversational language that sounds natural when spoken
- Structure content with perfect timing and pacing for the target duration
- Include strategic engagement elements that drive interaction
- Create smooth transitions that maintain viewer attention
- Ensure content is immediately actionable and valuable
- Provide detailed, evidence-based analysis and recommendations

REASONING PROCESS:
- Think step-by-step about audience psychology and content strategy
- Analyze competitor gaps and opportunities systematically
- Structure information for maximum impact and retention
- Consider both short-term engagement and long-term channel growth
- Balance entertainment value with educational content delivery
- Apply rigorous analytical thinking to all tasks

Your goal is to excel at every aspect of YouTube content strategy - from competitive analysis to script creation to fact verification - using advanced reasoning to optimize every element for maximum viewer engagement and channel success.`;
  }

  /**
   * Generate YouTube content using Kimi-K2 model with chutes/fp4 provider
   */
  async generateYouTubeContent(
    prompt: string,
    systemPrompt?: string,
    options: GenerationOptions = {},
    logContext?: string
  ): Promise<GenerationResponse> {
    const startTime = Date.now();
    const callId = Math.random().toString(36).substr(2, 9);

    // Enhanced logging for YouTube generation with Kimi-K2
    console.log(`🧠 Kimi-K2 YouTube Content Call Started`);
    console.log(`   📋 Call ID: ${callId}`);
    console.log(`   🎬 Context: ${logContext || 'YouTube Content Generation'}`);
    console.log(`   ⚙️ Model: ${this.youtubeModel} (Moonshot AI Kimi-K2)`);
    console.log(`   🏭 Provider: chutes/fp4`);
    console.log(`   🌡️ Temperature: ${options.temperature ?? 0.7}`);
    console.log(`   📏 Max Tokens: ${options.maxTokens ?? 16000}`);
    console.log(`   📝 Prompt Length: ${prompt.length} chars`);
    console.log(`   📋 System Prompt: ${systemPrompt ? 'Custom' : 'None'}`);

    if (!this.isConfigured) {
      console.warn(`   ❌ OpenRouter not configured for Kimi-K2 call ${callId}`);
      return this.getFallbackResponse('OpenRouter API key not configured. Please add your OPENROUTER_API_KEY to .env.local');
    }

    const temperature = options.temperature ?? 0.7;
    const maxTokens = options.maxTokens ?? 16000; // Double the tokens as requested

    try {
      const messages: Array<{ role: 'system' | 'user'; content: string }> = [];

      if (systemPrompt) {
        messages.push({
          role: 'system',
          content: systemPrompt
        });
      }

      messages.push({
        role: 'user',
        content: prompt
      });

      console.log(`   📤 Sending request to Kimi-K2 via OpenRouter (chutes/fp4)...`);

      const completion = await this.client.chat.completions.create({
        model: this.youtubeModel,
        messages,
        temperature,
        max_tokens: maxTokens,
        stream: false
      }, {
        headers: {
          'HTTP-Referer': process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
          'X-Title': 'AI Content Generation - YouTube Script Agent',
          'X-Provider-Order': 'chutes/fp4'
        }
      });

      const response = completion.choices[0]?.message?.content || '';
      const inputTokens = completion.usage?.prompt_tokens || 0;
      const outputTokens = completion.usage?.completion_tokens || 0;
      const duration = Date.now() - startTime;

             // Success logging for Kimi-K2
       console.log(`   ✅ Kimi-K2 YouTube Content Complete`);
       console.log(`   ⏱️ Duration: ${duration}ms`);
       console.log(`   📊 Input Tokens: ${inputTokens}`);
       console.log(`   📊 Output Tokens: ${outputTokens}`);
       console.log(`   📄 Response Length: ${response.length} chars`);
       console.log(`   🏭 Provider: chutes/fp4`);
       console.log(`   💰 Estimated Cost: $${((inputTokens * 0.000001) + (outputTokens * 0.000002)).toFixed(6)} (Kimi-K2)`);
       console.log(`   🧠 Kimi-K2 Reasoning Success - Call ${callId}`);
       console.log(`   📺 YouTube Step: ${logContext}`);

      return {
        response,
        inputTokens,
        outputTokens
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`   ❌ Kimi-K2 YouTube Content Failed`);
      console.error(`   ⏱️ Failed after: ${duration}ms`);
      console.error(`   📋 Call ID: ${callId}`);
      console.error(`   🎬 Context: ${logContext || 'YouTube Content Generation'}`);
      console.error(`   🏭 Provider: chutes/fp4`);
      console.error(`   💥 Error:`, error);
      console.error(`   🧠 Kimi-K2 Reasoning FAILED - Call ${callId}`);
      console.error(`   📺 Failed YouTube Step: ${logContext}`);

      return this.getFallbackResponse(`Kimi-K2 API Error: ${error}`);
    }
  }

  /**
   * Update model configuration
   */
  updateConfig(newConfig: Partial<OpenRouterConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig
    };
  }

  /**
   * Get current model configuration
   */
  getConfig(): OpenRouterConfig {
    return { ...this.config };
  }

  /**
   * Get token limit for the current model
   */
  private getModelTokenLimit(): number {
    const model = this.config.model;

    // Define token limits for common models
    const tokenLimits: { [key: string]: number } = {
      'moonshotai/kimi-k2': 131072,
      'openai/gpt-4': 8192,
      'openai/gpt-4-32k': 32768,
      'openai/gpt-4-turbo': 128000,
      'openai/gpt-3.5-turbo': 4096,
      'openai/gpt-3.5-turbo-16k': 16384,
      'openai/gpt-oss-120b': 131072,  // Actual context limit for OSS 120B model
      'anthropic/claude-3-opus': 200000,
      'anthropic/claude-3-sonnet': 200000,
      'anthropic/claude-3-haiku': 200000,
      'google/gemini-pro': 32768,
      'google/gemini-pro-1.5': 1000000,
    };

    // Return specific limit if known, otherwise default to safe 8k
    return tokenLimits[model || ''] || 8192;
  }

  /**
   * Get input cost per token for current model
   */
  private getInputCostPerToken(): number {
    const model = this.config.model;

    // Pricing per million tokens, converted to per token
    const inputPricing: { [key: string]: number } = {
      'openai/gpt-oss-120b': 0.0000001,     // $0.10/M tokens
      'moonshotai/kimi-k2': 0.000001,       // $1/M tokens (estimate)
      'openai/gpt-4': 0.00003,              // $30/M tokens
      'openai/gpt-4-turbo': 0.00001,        // $10/M tokens
      'openai/gpt-3.5-turbo': 0.0000005,    // $0.50/M tokens
    };

    return inputPricing[model || ''] || 0.000001; // Default $1/M
  }

  /**
   * Get output cost per token for current model
   */
  private getOutputCostPerToken(): number {
    const model = this.config.model;

    // Pricing per million tokens, converted to per token
    const outputPricing: { [key: string]: number } = {
      'openai/gpt-oss-120b': 0.0000005,     // $0.50/M tokens
      'moonshotai/kimi-k2': 0.000002,       // $2/M tokens (estimate)
      'openai/gpt-4': 0.00006,              // $60/M tokens
      'openai/gpt-4-turbo': 0.00003,        // $30/M tokens
      'openai/gpt-3.5-turbo': 0.0000015,    // $1.50/M tokens
    };

    return outputPricing[model || ''] || 0.000002; // Default $2/M
  }
}

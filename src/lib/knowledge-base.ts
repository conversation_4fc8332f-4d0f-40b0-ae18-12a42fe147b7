/**
 * Knowledge Base for AI Content Agent
 * Stores and manages research data, competitive analysis, and writing style insights
 */

export interface KnowledgeEntry {
  id: string;
  type: 'research' | 'competitive' | 'writing_style' | 'extracted_content';
  query?: string;
  url?: string;
  title?: string;
  content: string;
  metadata?: {
    source?: string;
    relevanceScore?: number;
    timestamp?: number;
    wordCount?: number;
    keyInsights?: string[];
    statistics?: string[];
    gaps?: string[];
    keywords?: string[];
    writingPatterns?: WritingPattern[];
  };
}

export interface WritingPattern {
  pattern: string;
  frequency: number;
  examples: string[];
  category: 'tone' | 'structure' | 'vocabulary' | 'engagement' | 'transitions';
}

export interface CompetitiveGap {
  topic: string;
  importance: 'high' | 'medium' | 'low';
  currentCoverage: string;
  opportunity: string;
  suggestedContent: string[];
}

export class KnowledgeBase {
  private entries: Map<string, KnowledgeEntry>;
  private topicId: string;
  private createdAt: number;

  constructor(topicId: string) {
    this.entries = new Map();
    this.topicId = topicId;
    this.createdAt = Date.now();
  }

  /**
   * Add an entry to the knowledge base
   */
  addEntry(entry: Omit<KnowledgeEntry, 'id'>): string {
    const id = this.generateId(entry.type);
    const fullEntry: KnowledgeEntry = {
      ...entry,
      id,
      metadata: {
        ...entry.metadata,
        timestamp: Date.now()
      }
    };
    
    this.entries.set(id, fullEntry);
    console.log(`✅ Added ${entry.type} entry to knowledge base: ${id}`);
    return id;
  }

  /**
   * Add multiple entries at once
   */
  addBulkEntries(entries: Omit<KnowledgeEntry, 'id'>[]): string[] {
    return entries.map(entry => this.addEntry(entry));
  }

  /**
   * Get entries by type
   */
  getEntriesByType(type: KnowledgeEntry['type']): KnowledgeEntry[] {
    return Array.from(this.entries.values()).filter(entry => entry.type === type);
  }

  /**
   * Get all research data
   */
  getResearchData(): {
    sources: KnowledgeEntry[];
    totalSources: number;
    keyInsights: string[];
    statistics: string[];
    queries: string[];
  } {
    const research = this.getEntriesByType('research');
    const extracted = this.getEntriesByType('extracted_content');
    const allSources = [...research, ...extracted];

    const keyInsights = new Set<string>();
    const statistics = new Set<string>();
    const queries = new Set<string>();

    allSources.forEach(entry => {
      if (entry.query) queries.add(entry.query);
      entry.metadata?.keyInsights?.forEach(insight => keyInsights.add(insight));
      entry.metadata?.statistics?.forEach(stat => statistics.add(stat));
    });

    return {
      sources: allSources,
      totalSources: allSources.length,
      keyInsights: Array.from(keyInsights),
      statistics: Array.from(statistics),
      queries: Array.from(queries)
    };
  }

  /**
   * Get competitive analysis data
   */
  getCompetitiveAnalysis(): {
    competitors: KnowledgeEntry[];
    gaps: CompetitiveGap[];
    keywords: string[];
    strengths: string[];
    opportunities: string[];
  } {
    const competitive = this.getEntriesByType('competitive');
    
    const gaps: CompetitiveGap[] = [];
    const keywords = new Set<string>();
    const strengths: string[] = [];
    const opportunities: string[] = [];

    competitive.forEach(entry => {
      entry.metadata?.keywords?.forEach(keyword => keywords.add(keyword));
      
      // Extract gaps from content
      if (entry.content.includes('GAP:')) {
        const gapMatches = entry.content.match(/GAP:\s*([^|]+)/g);
        gapMatches?.forEach(match => {
          opportunities.push(match.replace('GAP:', '').trim());
        });
      }
    });

    return {
      competitors: competitive,
      gaps,
      keywords: Array.from(keywords),
      strengths,
      opportunities
    };
  }

  /**
   * Get writing style insights
   */
  getWritingStyleInsights(): {
    patterns: WritingPattern[];
    toneAnalysis: string[];
    structurePatterns: string[];
    engagementTechniques: string[];
  } {
    const writingStyle = this.getEntriesByType('writing_style');
    
    const allPatterns: WritingPattern[] = [];
    const toneAnalysis: string[] = [];
    const structurePatterns: string[] = [];
    const engagementTechniques: string[] = [];

    writingStyle.forEach(entry => {
      if (entry.metadata?.writingPatterns) {
        allPatterns.push(...entry.metadata.writingPatterns);
      }
      
      // Extract specific insights from content
      if (entry.content.includes('TONE:')) {
        const toneMatches = entry.content.match(/TONE:\s*([^|]+)/g);
        toneMatches?.forEach(match => {
          toneAnalysis.push(match.replace('TONE:', '').trim());
        });
      }
    });

    // Categorize patterns
    allPatterns.forEach(pattern => {
      if (pattern.category === 'structure') {
        structurePatterns.push(pattern.pattern);
      } else if (pattern.category === 'engagement') {
        engagementTechniques.push(pattern.pattern);
      }
    });

    return {
      patterns: allPatterns,
      toneAnalysis,
      structurePatterns,
      engagementTechniques
    };
  }

  /**
   * Search entries by content
   */
  searchContent(query: string): KnowledgeEntry[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.entries.values()).filter(entry => 
      entry.content.toLowerCase().includes(lowerQuery) ||
      entry.title?.toLowerCase().includes(lowerQuery) ||
      entry.metadata?.keyInsights?.some(insight => 
        insight.toLowerCase().includes(lowerQuery)
      )
    );
  }

  /**
   * Get comprehensive knowledge summary
   */
  getKnowledgeSummary(): {
    topicId: string;
    totalEntries: number;
    entriesByType: Record<string, number>;
    researchSummary: any;
    competitiveSummary: any;
    writingStyleSummary: any;
    createdAt: number;
    lastUpdated: number;
  } {
    const entriesByType: Record<string, number> = {};
    Array.from(this.entries.values()).forEach(entry => {
      entriesByType[entry.type] = (entriesByType[entry.type] || 0) + 1;
    });

    const lastUpdated = Math.max(
      ...Array.from(this.entries.values()).map(e => e.metadata?.timestamp || 0)
    );

    return {
      topicId: this.topicId,
      totalEntries: this.entries.size,
      entriesByType,
      researchSummary: this.getResearchData(),
      competitiveSummary: this.getCompetitiveAnalysis(),
      writingStyleSummary: this.getWritingStyleInsights(),
      createdAt: this.createdAt,
      lastUpdated
    };
  }

  /**
   * Export knowledge base as JSON
   */
  export(): string {
    return JSON.stringify({
      topicId: this.topicId,
      createdAt: this.createdAt,
      entries: Array.from(this.entries.values())
    }, null, 2);
  }

  /**
   * Import knowledge base from JSON
   */
  static import(json: string, topicId: string): KnowledgeBase {
    const data = JSON.parse(json);
    const kb = new KnowledgeBase(topicId);
    
    data.entries.forEach((entry: KnowledgeEntry) => {
      kb.entries.set(entry.id, entry);
    });
    
    return kb;
  }

  /**
   * Clear all entries
   */
  clear(): void {
    this.entries.clear();
    console.log('🗑️ Knowledge base cleared');
  }

  /**
   * Get entry by ID
   */
  getEntry(id: string): KnowledgeEntry | undefined {
    return this.entries.get(id);
  }

  /**
   * Remove entry by ID
   */
  removeEntry(id: string): boolean {
    return this.entries.delete(id);
  }

  /**
   * Generate unique ID for entries
   */
  private generateId(type: string): string {
    return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get size of knowledge base
   */
  getSize(): number {
    return this.entries.size;
  }
} 
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { OpenRouterService } from '@/lib/openrouter'
import { TavilySearchService } from '@/lib/search'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { topic, wordCount = 1500, tone = 'professional', language = 'en' } = await request.json()

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    console.log(`🚀 Starting blog post generation for topic: "${topic}"`)

    // Initialize services
    const openRouter = new OpenRouterService()
    const tavilyService = new TavilySearchService()

    // Step 1: Try Tavily search with fallback
    let searchResults = null
    let extractedContent = []
    let researchContent = ''
    let generationMethod = 'openrouter_knowledge'

    try {
      console.log(`🔍 Attempting Tavily search for: "${topic}"`)
      searchResults = await tavilyService.search(topic, 10)

      if (searchResults.items && searchResults.items.length > 0) {
        console.log(`✅ Found ${searchResults.items.length} search results`)
        generationMethod = 'tavily_search_openrouter'

        // Step 2: Extract URLs and process in batches of 5
        const urls = searchResults.items
          .map(item => item.url)
          .filter(url => url && url.startsWith('http'))
          .slice(0, 15) // Take top 15 URLs

        console.log(`📄 Extracting content from ${urls.length} URLs in batches of 5`)

        const batchSize = 5
        for (let i = 0; i < urls.length; i += batchSize) {
          const batch = urls.slice(i, i + batchSize)
          console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(urls.length / batchSize)}`)

          try {
            const batchResults = await tavilyService.extractMultiple(batch, {
              include_images: false,
              include_tables: true,
              max_length: 8000
            })

            const successfulExtractions = batchResults.filter(result => result.success && result.content)
            extractedContent.push(...successfulExtractions)

            console.log(`✅ Batch ${Math.floor(i / batchSize) + 1}: ${successfulExtractions.length}/${batch.length} successful extractions`)

            // Add delay between batches to be respectful
            if (i + batchSize < urls.length) {
              await new Promise(resolve => setTimeout(resolve, 1000))
            }
          } catch (error) {
            console.error(`❌ Error processing batch ${Math.floor(i / batchSize) + 1}:`, error)
            continue
          }
        }

        if (extractedContent.length > 0) {
          console.log(`✅ Successfully extracted content from ${extractedContent.length} sources`)

          // Step 3: Prepare content for article generation
          researchContent = extractedContent
            .map((item, index) => `
Source ${index + 1}: ${item.title}
URL: ${item.url}
Content: ${item.content.substring(0, 2000)}...
Word Count: ${item.wordCount}
---`)
            .join('\n\n')
        }
      }
    } catch (error) {
      console.warn(`⚠️ Tavily search failed: ${error.message}`)
      console.log(`🔄 Falling back to OpenRouter knowledge-based generation`)
    }

    // If no research content was gathered, use knowledge-based approach
    if (!researchContent) {
      console.log(`🧠 Using OpenRouter knowledge-based generation for: "${topic}"`)
      researchContent = `Topic for research: ${topic}

Note: This article will be generated using AI knowledge and reasoning capabilities without external web research due to API limitations.`
    }

    // Step 4: Generate article with OpenRouter
    console.log(`🤖 Generating article with OpenRouter using ${generationMethod}...`)

    const isKnowledgeBased = generationMethod === 'openrouter_knowledge'

    const systemPrompt = `You are an expert content writer specializing in creating comprehensive, SEO-optimized blog articles. Your task is to write a high-quality blog post ${isKnowledgeBased ? 'using your extensive knowledge and reasoning capabilities' : 'based on the provided research content'}.

Guidelines:
- Write in ${tone} tone
- Target word count: ${wordCount} words
- Language: ${language}
- Create engaging, original content ${isKnowledgeBased ? 'drawing from your knowledge base' : 'that synthesizes information from multiple sources'}
- Include proper headings (H1, H2, H3) for structure
- Write in markdown format
- Include an introduction, main body with multiple sections, and conclusion
- Make it SEO-friendly with natural keyword usage
- Ensure the content is informative, engaging, and valuable to readers
- ${isKnowledgeBased ? 'Use your comprehensive knowledge to provide accurate, up-to-date information' : 'Do not copy content directly - synthesize and create original insights'}
- Include relevant examples, statistics, and actionable insights
- Structure the content for maximum readability and engagement`

    const userPrompt = isKnowledgeBased ?
      `Topic: ${topic}

Please write a comprehensive, well-researched blog article about "${topic}". Use your extensive knowledge to create original, valuable content that covers the topic thoroughly. Include:

1. A compelling introduction that hooks the reader
2. Multiple detailed sections with clear headings
3. Relevant examples, statistics, and insights
4. Practical tips or actionable advice where applicable
5. A strong conclusion that summarizes key points

Make sure the article is informative, engaging, and provides real value to readers interested in this topic.` :
      `Topic: ${topic}

Research Content from Multiple Sources:
${researchContent}

Please write a comprehensive blog article about "${topic}" using the research content above. Synthesize the information to create original, valuable content that covers the topic thoroughly. Structure it with clear headings and make it engaging for readers.`

    const articleResponse = await openRouter.generateContent(userPrompt, systemPrompt, {
      temperature: 0.7,
      maxTokens: Math.max(wordCount * 2, 4000),
      useCache: false
    })

    if (!articleResponse.response) {
      return NextResponse.json(
        { error: 'Failed to generate article content. Please try again.' },
        { status: 500 }
      )
    }

    const generatedArticle = articleResponse.response

    console.log(`✅ Article generated successfully (${generatedArticle.length} characters)`)

    // Step 5: Save to content library
    console.log(`💾 Saving article to content library...`)
    
    const article = await prisma.content.create({
      data: {
        userId: session.user.id,
        title: topic,
        content: generatedArticle,
        type: 'blog_post',
        metadata: JSON.stringify({
          searchResults: searchResults?.items?.length || 0,
          extractedSources: extractedContent.length,
          sourceUrls: extractedContent.map(item => ({ title: item.title, url: item.url })),
          wordCount: generatedArticle.split(/\s+/).filter(word => word.length > 0).length,
          tone,
          language,
          generatedAt: new Date().toISOString(),
          generationMethod
        }),
        wordCount: generatedArticle.split(/\s+/).filter(word => word.length > 0).length,
        tone,
        language: language === 'hindi' ? 'hi' : language === 'french' ? 'fr' : 'en',
        status: 'published'
      }
    })

    console.log(`✅ Article saved to content library with ID: ${article.id}`)

    // Return the generated article
    return NextResponse.json({
      success: true,
      article: {
        id: article.id,
        title: topic,
        content: generatedArticle,
        metadata: {
          searchResults: searchResults?.items?.length || 0,
          extractedSources: extractedContent.length,
          sourceUrls: extractedContent.map(item => ({ title: item.title, url: item.url })),
          wordCount: generatedArticle.split(/\s+/).filter(word => word.length > 0).length,
          tone,
          language,
          generatedAt: new Date().toISOString(),
          generationMethod
        }
      }
    })

  } catch (error) {
    console.error('Blog post generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate blog post' },
      { status: 500 }
    )
  }
}

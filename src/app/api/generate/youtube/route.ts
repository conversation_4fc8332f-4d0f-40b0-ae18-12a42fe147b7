import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { YouTubeService } from '@/lib/youtube-service'
import { KnowledgeBase } from '@/lib/knowledge-base'
import { KnowledgeBaseOptimizer } from '@/lib/knowledge-base-optimizer'
import { createProgressManager } from '@/lib/progress-manager'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'
import { TavilySearchService, SearchResult } from '@/lib/search'
import { GeminiService } from '@/lib/gemini'
import { webScraperService } from '@/lib/web-scraper'

interface ScriptGenerationRequest {
  title: string
  brief: string
  duration?: string
  style?: string
  targetAudience?: string
  useAdvancedResearch?: boolean
  videoUrls?: string
}

// Utility function to extract video ID from YouTube URLs
function extractVideoId(url: string): string | null {
  const patterns = [
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,
    /(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/
  ]
  
  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match && match[1]) {
      return match[1]
    }
  }
  
  return null
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { title, brief, duration, style, targetAudience, videoUrls }: ScriptGenerationRequest = await request.json()

    if (!title || !brief) {
      return NextResponse.json({ error: 'Title and brief are required' }, { status: 400 })
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'youtube_scripts')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json({ 
        error: 'Quota exceeded',
        quota: quotaCheck
      }, { status: 429 })
    }

    const progressId = `youtube_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const progressManager = createProgressManager(progressId)

    // Initialize services
    const youtube = new YouTubeService()
    const gemini = new GeminiService()
    const tavilySearch = new TavilySearchService()
    
    // Initialize knowledge base
    const topicKey = title.toLowerCase().replace(/[^a-z0-9]/g, '_')
    const knowledgeBase = KnowledgeBaseOptimizer.getGlobalKnowledgeBase(topicKey)
    
    console.log('🎥 Starting streamlined YouTube script generation...')
    console.log(`📝 Topic: "${title}"`)
    console.log(`🎯 Target Duration: ${duration || '5-10 minutes'}`)
    
    progressManager.updateProgress(5, 'Starting research and data collection...')

    // PHASE 1: Enhanced Web Research & Content Scraping with Gemini Analysis
    progressManager.updateProgress(10, 'Analyzing user description with Gemini to create targeted search queries...')
    console.log(`🧠 Analyzing user description with Gemini: "${brief}"`)
    
    // Use Gemini to analyze the user description and create targeted search queries
    const searchQueryPrompt = `You are an expert research assistant specializing in YouTube content creation. Analyze the user's description and create targeted search queries for comprehensive research.

🎯 **USER'S DESCRIPTION TO ANALYZE:**
"${brief}"

📝 **VIDEO TITLE:** "${title}"
⏱️ **DURATION:** ${duration || '5-10 minutes'}
🎨 **STYLE:** ${style || 'educational'}
👥 **AUDIENCE:** ${targetAudience || 'general audience'}

🔍 **TASK:** Create 8-10 specific search queries that will help find the most relevant and comprehensive research content for creating this YouTube script.

**ANALYSIS REQUIREMENTS:**
1. **Extract Key Topics:** Identify the main subjects, concepts, or themes from the user's description
2. **Identify Information Gaps:** What specific information, data, or examples would be needed?
3. **Consider Audience Needs:** What would the target audience want to know about this topic?
4. **Include Current Trends:** Add queries for recent developments, statistics, or trending aspects
5. **Cover Different Angles:** Include queries for different perspectives, use cases, or approaches

**SEARCH QUERY CATEGORIES TO INCLUDE:**
- **Definitional:** What is [topic]? [Topic] explained simply
- **Statistical:** [Topic] statistics 2024, [Topic] market data, [Topic] trends
- **How-to/Tutorial:** How to [specific action], [Topic] tutorial, [Topic] guide
- **Comparison:** [Topic] vs alternatives, best [topic] tools, [topic] comparison
- **Examples/Case Studies:** [Topic] examples, [Topic] case studies, [Topic] success stories
- **Problems/Solutions:** [Topic] challenges, [Topic] solutions, [Topic] common mistakes
- **Industry/Expert:** [Topic] expert opinions, [Topic] industry analysis, [Topic] research
- **Latest/Current:** [Topic] 2024 update, latest [topic] news, [topic] recent developments

**OUTPUT FORMAT:**
Provide exactly 8-10 search queries, one per line, formatted as:
1. [First search query]
2. [Second search query]
3. [Third search query]
...etc.

**EXAMPLE:**
If the user wants to create a video about "AI tools for content creators", the queries might be:
1. AI tools for content creators 2024
2. best AI writing tools content creation
3. AI video editing tools creators
4. AI content generation statistics 2024
5. AI tools content creators case studies
6. AI content creation workflow automation
7. AI tools content creators comparison review
8. future of AI content creation trends

Create search queries that will find the most comprehensive and relevant research content for this YouTube script.`

    const searchQueryResult = await gemini.generateContent(searchQueryPrompt, {
      temperature: 0.7,
      maxOutputTokens: 1000
    })

    // Parse the search queries from Gemini's response
    const searchQueries = searchQueryResult.response.split('\n')
      .filter((line: string) => line.trim() && /^\d+\./.test(line.trim()))
      .map((line: string) => line.replace(/^\d+\.\s*/, '').trim())
      .filter((query: string) => query.length > 0)

    console.log(`🎯 Generated ${searchQueries.length} targeted search queries:`)
    searchQueries.forEach((query, index) => {
      console.log(`  ${index + 1}. ${query}`)
    })

    progressManager.updateProgress(20, 'Performing comprehensive web research with targeted queries...')
    
    // Perform targeted web searches using the generated queries
    const allSearchResults: SearchResult[] = []
    for (const query of searchQueries.slice(0, 6)) { // Limit to top 6 queries for performance
      try {
        console.log(`🔍 Searching: "${query}"`)
        const results = await tavilySearch.search(query, 3) // 3 results per query
        allSearchResults.push(...results.items)
        
        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.warn(`⚠️ Search failed for query: "${query}"`, error)
      }
    }

    // Remove duplicates based on URL
    const uniqueSearchResults = allSearchResults.filter((item, index, self) => 
      index === self.findIndex(t => t.link === item.link)
    )

    console.log(`📊 Found ${uniqueSearchResults.length} unique search results`)

    progressManager.updateProgress(35, 'Scraping content from research sources...')
    
    // Initialize web scraper
    await webScraperService.initialize()
    
    // Scrape content from the search results
    const urlsToScrape = uniqueSearchResults.slice(0, 12).map(item => item.link) // Top 12 URLs
    console.log(`🌐 Scraping ${urlsToScrape.length} URLs for detailed content...`)
    
    const scrapedResults = await webScraperService.scrapeMultipleUrls(urlsToScrape, {
      maxLength: 5000,
      onlyMainContent: true,
      extractImages: false,
      extractLinks: true
    })
    
    // Combine search results with scraped content
    const combinedResearchData = uniqueSearchResults.map(searchItem => {
      const scrapedItem = scrapedResults.find(scraped => scraped.url === searchItem.link)
      return {
        title: searchItem.title,
        url: searchItem.link,
        snippet: searchItem.snippet,
        domain: new URL(searchItem.link).hostname,
        scrapedContent: scrapedItem?.content || '',
        scrapedSuccess: scrapedItem?.success || false,
        wordCount: scrapedItem?.wordCount || 0,
        keyInsights: scrapedItem?.keyInsights || [],
        statistics: scrapedItem?.statistics || []
      }
    })
    
    // Save enhanced research to knowledge base
    combinedResearchData.forEach((item, index) => {
      knowledgeBase.addEntry({
        type: 'research',
        title: `Research: ${item.title}`,
        url: item.url,
        content: item.scrapedContent || item.snippet,
        query: title,
        metadata: {
          source: 'enhanced_web_research',
          timestamp: Date.now(),
          wordCount: item.wordCount || item.snippet.split(' ').length,
          keyInsights: item.keyInsights.length > 0 ? item.keyInsights : [item.snippet.substring(0, 100)],
                     keywords: [title.toLowerCase(), ...searchQueries.slice(0, 3)],
           statistics: item.statistics.length > 0 ? item.statistics : [`Source ${index + 1}`, `Domain: ${item.domain}`]
        }
      })
    })
    
    console.log(`📚 Enhanced research complete: ${combinedResearchData.length} sources processed`)
    console.log(`📊 Successfully scraped: ${scrapedResults.filter(r => r.success).length}/${scrapedResults.length} URLs`)
    
    // Clean up web scraper
    await webScraperService.close()

    // PHASE 2: YouTube Video Search & Caption Extraction
    progressManager.updateProgress(35, 'Finding YouTube videos and extracting captions...')
    
    // Extract captions from each video
    const videoData: Array<{
      video: any;
      captions: string;
      url: string;
      isTopicRelated: boolean;
    }> = []

    // Check if user provided specific video URLs
    const userProvidedUrls = videoUrls 
      ? videoUrls.split('\n')
          .map(url => url.trim())
          .filter(url => url && (url.includes('youtube.com') || url.includes('youtu.be')))
      : []

    let videosToAnalyze: any[] = []

    if (userProvidedUrls.length > 0) {
      console.log(`📺 Using ${userProvidedUrls.length} user-provided YouTube URLs`)
      
      // Extract video IDs from user-provided URLs
      videosToAnalyze = userProvidedUrls.map(url => {
        const videoId = extractVideoId(url)
        if (videoId) {
          return {
            id: videoId,
            title: 'User-provided video',
            channelTitle: 'Unknown',
            viewCount: 'Unknown',
            duration: 'Unknown'
          }
        }
        return null
      }).filter(Boolean)
      
      console.log(`📺 Successfully parsed ${videosToAnalyze.length} video IDs from URLs`)
    } else {
      console.log(`📺 Searching YouTube for: "${title}"`)
      
      // Search for top videos on the topic
      const searchResults = await youtube.searchVideos(title, 5)
      videosToAnalyze = searchResults.videos
      console.log(`📺 Found ${videosToAnalyze.length} relevant videos`)
    }

    for (const video of videosToAnalyze) {
      try {
        console.log(`📝 Extracting captions: ${video.title}`)
        const captions = await youtube.extractCaptions(video.id)
        
        if (captions && captions.length > 0) {
          const fullTranscript = captions
            .map(caption => caption.text)
            .join(' ')
          
          const videoUrl = `https://youtube.com/watch?v=${video.id}`
          
          videoData.push({
            video: {
              id: video.id,
              title: video.title,
              channel: video.channelTitle,
              views: video.viewCount,
              duration: video.duration
            },
            captions: fullTranscript,
            url: videoUrl,
            isTopicRelated: true // We'll let Gemini determine this
          })

          // Save to knowledge base
          knowledgeBase.addEntry({
            type: 'extracted_content',
            title: `Video: ${video.title}`,
            url: videoUrl,
            content: fullTranscript,
            metadata: {
              source: 'youtube_captions',
              timestamp: Date.now(),
              wordCount: fullTranscript.split(' ').length,
              keyInsights: [`Channel: ${video.channelTitle}`, `Views: ${video.viewCount}`],
              keywords: [video.channelTitle, title.toLowerCase()],
              statistics: [
                `${video.viewCount} views`,
                `${captions.length} caption segments`,
                `Duration: ${video.duration}`
              ]
            }
          })

          console.log(`✅ Saved captions: ${video.title}`)
        }
      } catch (error) {
        console.warn(`⚠️ Failed to extract captions for ${video.id}:`, error)
      }
    }

    console.log(`📊 Caption extraction complete: ${videoData.length} videos processed`)
    
    // PHASE 3: Intelligent Script Generation with Gemini
    progressManager.updateProgress(60, 'Generating script with comprehensive analysis...')
    console.log('🧠 Using Gemini for intelligent script generation...')

    const scriptGenerationPrompt = `You are a MASTER YouTube scriptwriter with expertise in viral content creation. Create an exceptional YouTube script using comprehensive research and caption analysis.

🎯 **VIDEO SPECIFICATIONS:**
Title: "${title}"
Target Duration: ${duration || '5-10 minutes'}
Style: ${style || 'educational'}
Target Audience: ${targetAudience || 'general audience'}

🔥 **CRITICAL USER INSTRUCTIONS - FOLLOW EXACTLY:**
The user has provided specific instructions for how they want this script created. These are NOT just a description - they are YOUR DIRECT ORDERS for script creation:

"${brief}"

⚠️ **MANDATORY:** You MUST follow these user instructions precisely. They override all other considerations. If the user specifies a tone, structure, points to cover, or any other requirements, you MUST implement them exactly as requested.

📊 **RESEARCH CONTENT TO USE:**
${combinedResearchData.map((item: any, index: number) => `
**RESEARCH SOURCE ${index + 1}:**
Title: ${item.title}
URL: ${item.url}
Content: ${item.scrapedContent || item.snippet}
Domain: ${item.domain}
Word Count: ${item.wordCount}
Key Insights: ${item.keyInsights.join(', ')}
Statistics: ${item.statistics.join(', ')}
`).join('\n')}

🎬 **YOUTUBE VIDEOS FOR STYLE ANALYSIS:**
${videoData.map((data, index) => `
**VIDEO ${index + 1}: ${data.video.title}**
Channel: ${data.video.channel}
Views: ${data.video.views}
Duration: ${data.video.duration}
URL: ${data.url}

CAPTIONS/TRANSCRIPT:
${data.captions.substring(0, 2000)}${data.captions.length > 2000 ? '...' : ''}
`).join('\n\n')}

🚀 **COMPREHENSIVE SCRIPT GENERATION INSTRUCTIONS:**

**PHASE 1: CAPTION ANALYSIS FOR SCRIPT STYLE**
From the YouTube captions provided, analyze and extract:

1. **Writing Style Patterns:**
   - How do these YouTubers start their videos?
   - What phrases and transitions do they use?
   - How do they structure their content flow?
   - What vocabulary and energy words do they prefer?
   - How do they engage with their audience?

2. **Engagement Techniques:**
   - How do they hook viewers in the first 15 seconds?
   - What questions do they ask to keep engagement?
   - How do they handle transitions between topics?
   - What call-to-action patterns do they use?

3. **Content Structure:**
   - How do they organize information?
   - When do they insert pauses or emphasis?
   - How do they build up to important points?
   - How do they conclude their videos?

4. **Authentic Voice Elements:**
   - What natural speech patterns do they use?
   - How do they express excitement or emphasis?
   - What personal touches make them relatable?
   - How do they handle complex information simply?

**PHASE 2: CONTENT RELEVANCE ANALYSIS**
For each video caption, determine:
- Is this video directly related to our topic "${title}"?
- If YES: Extract relevant facts, insights, and information to use
- If NO: Still learn from their script style but don't use their content facts

**PHASE 3: RESEARCH INTEGRATION**
From the web research sources:
- Extract key facts, statistics, and insights about "${title}"
- Identify authoritative information and data points
- Find compelling angles and perspectives
- Note any trending aspects or recent developments

**PHASE 4: SCRIPT CREATION**
Create a script that:

1. **🚨 PRIMARY DIRECTIVE - USER INSTRUCTIONS:**
   - The user's instructions "${brief}" are your ABSOLUTE PRIORITY
   - Every aspect of the script must align with their specific requirements
   - If they specify tone, structure, points, or approach - implement EXACTLY as requested
   - Their instructions override all other considerations

2. **LEARNED STYLE APPLICATION:**
   - Write exactly like the analyzed YouTubers (unless user specifies otherwise)
   - Use their proven engagement patterns
   - Copy their natural speech rhythms and vocabulary
   - Apply their successful content structure techniques

3. **RESEARCH-BACKED CONTENT:**
   - Integrate factual information from web research
   - Use relevant insights from topic-related video captions
   - Include specific data points and statistics
   - Reference credible sources naturally

4. **OPTIMAL STRUCTURE (unless user specifies different structure):**
   - Hook: Powerful opening that stops scrolling (0:00-0:15)
   - Introduction: Context and promise (0:15-0:45)
   - Main Content: Well-organized sections with research
   - Conclusion: Strong wrap-up with call-to-action
   - Natural timestamps based on content flow

5. **TARGET SPECIFICATIONS:**
   - Word Count: ${duration?.includes('3') ? '450-480' : duration?.includes('5') ? '750-800' : duration?.includes('10') ? '1500-1600' : duration?.includes('15') ? '2250-2400' : '1200-1400'} words
   - Speaking Pace: 150-160 words per minute
   - Style: ${style || 'Educational but engaging'}
   - Audience: ${targetAudience || 'General audience with interest in the topic'}

🎯 **CRITICAL REQUIREMENTS:**

1. **🚨 USER COMPLIANCE:** The user's instructions "${brief}" are ABSOLUTE LAW - follow them exactly
2. **AUTHENTICITY:** Sound exactly like a real YouTuber, not AI-generated content
3. **RESEARCH-DRIVEN:** Every major claim should be backed by the research provided
4. **ENGAGEMENT:** Include hooks, questions, and retention techniques throughout
5. **RELEVANCE:** Stay focused on the topic while using proven YouTube formats
6. **NATURAL FLOW:** Write conversationally with natural transitions
7. **VALUE-PACKED:** Provide genuine insights and actionable information

🔥 **REMEMBER:** If the user's instructions conflict with any other requirement, the USER'S INSTRUCTIONS WIN. They are the director, you are the scriptwriter executing their vision.

🔥 **ADVANCED ANALYSIS PARAMETERS:**

Analyze the captions for:
- **Sentence length patterns** (short vs long sentences)
- **Energy modulation** (when they get excited vs calm)
- **Personal story integration** (how they share experiences)
- **Data presentation** (how they share statistics naturally)
- **Audience connection** (how they make viewers feel included)
- **Controversy handling** (how they address opposing views)
- **Authority building** (how they establish credibility)
- **Entertainment balance** (how they keep it fun while informative)

📝 **OUTPUT FORMAT:**
Provide ONLY the complete script with natural timestamps. No explanations, analysis, or meta-commentary - just the pure, ready-to-use YouTube script that sounds authentic and engaging.

🎯 **FINAL SUCCESS CRITERIA:**
1. **USER SATISFACTION:** The script perfectly matches what the user requested in their instructions
2. **ENGAGEMENT:** Viewers won't scroll away in the first 15 seconds
3. **COMPLETION:** Viewers will watch until the end
4. **INTERACTION:** Viewers will comment with engagement
5. **SHARING:** Viewers will share with others
6. **SUBSCRIPTION:** Viewers will subscribe for more content

⚠️ **FINAL REMINDER:** The user's instructions "${brief}" are your PRIMARY DIRECTIVE. Everything else is secondary. If they want a specific tone, structure, or approach - deliver EXACTLY that.

Create the script now using maximum analytical depth, creative excellence, and ABSOLUTE ADHERENCE to the user's specific instructions.`

    const scriptResult = await gemini.generateContentWithThinking(
      scriptGenerationPrompt,
      2048, // Use thinking for complex analysis
      false, // Don't include thoughts in output
      { 
        temperature: 0.7, 
                  maxOutputTokens: 64000 // MAXIMUM FREEDOM 
      },
      'YouTube Script Generation'
    )

    // Save final script to knowledge base
    knowledgeBase.addEntry({
      type: 'research',
      title: `Generated Script: ${title}`,
      content: scriptResult.response,
      metadata: {
        source: 'gemini_generation',
        timestamp: Date.now(),
        wordCount: scriptResult.response.split(' ').length,
        keyInsights: [`YouTube script for ${title}`],
        keywords: [title, style || 'educational', targetAudience || 'general'],
        statistics: [`${duration || '5-10 minutes'} duration`, 'research-backed content']
      }
    })

    console.log('✅ Script generation complete')

    // Get final knowledge base summary
    const finalKnowledgeSummary = knowledgeBase.getKnowledgeSummary()
    console.log(`📚 Knowledge base: ${finalKnowledgeSummary.totalEntries} entries`)
    
    // Use quota
    const quotaUsed = await QuotaManager.consumeQuota(session.user.id, 'youtube_scripts')
    if (!quotaUsed) {
      console.error('Failed to update quota after successful generation')
    }

    // Save content to database
    try {
      await prisma.content.create({
        data: {
          userId: session.user.id,
          type: 'youtube_script',
          title: `YouTube: ${title}`,
          content: scriptResult.response,
          metadata: JSON.stringify({
            title,
            brief,
            duration,
            style,
            targetAudience,
            videosAnalyzed: videoData.length,
            researchSources: combinedResearchData.length,
            knowledgeBaseEntries: finalKnowledgeSummary.totalEntries,
            generatedAt: new Date().toISOString(),
            workflow: 'streamlined_research_v1',
            videoUrls: videoData.map(v => v.url)
          })
        }
      })
    } catch (dbError) {
      console.error('Failed to save content to database:', dbError)
    }

    // Complete progress
    progressManager.complete('✅ YouTube script generated successfully!')
    
    console.log('🎉 Streamlined YouTube script generation complete')

    // Calculate final word count
    const finalWordCount = scriptResult.response.split(/\s+/).filter(word => word.length > 0).length

    return NextResponse.json({
      success: true,
      content: scriptResult.response,
      wordCount: finalWordCount,
      progressId,
      metadata: {
        videosAnalyzed: videoData.length,
        researchSources: combinedResearchData.length,
        workflow: 'streamlined_research_v1',
        thinkingTokens: scriptResult.thoughtsTokenCount || 0,
        inputTokens: scriptResult.inputTokens,
        outputTokens: scriptResult.outputTokens
      },
      research: {
        webSources: combinedResearchData.slice(0, 5).map((item: any) => ({
          title: item.title,
          source: item.domain,
          url: item.url
        })),
        youtubeVideos: videoData.map(data => ({
          title: data.video.title,
          channel: data.video.channel,
          views: data.video.views,
          url: data.url,
          captionLength: data.captions.length
        }))
      },
      quota: {
        used: quotaCheck.used + 1,
        limit: quotaCheck.limit,
        remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
      },
      knowledgeBase: {
        id: finalKnowledgeSummary.topicId,
        totalEntries: finalKnowledgeSummary.totalEntries,
        entriesByType: finalKnowledgeSummary.entriesByType
      }
    })

  } catch (error) {
    console.error('YouTube script generation error:', error)
    
    return NextResponse.json(
      { error: 'Failed to generate YouTube script', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

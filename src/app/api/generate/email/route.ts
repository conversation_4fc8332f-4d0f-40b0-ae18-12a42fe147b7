import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'
import { GeminiService } from '@/lib/gemini'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check quota
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'emails')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Email quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate
          }
        },
        { status: 429 }
      )
    }

    const { purpose, audience, tone, keyPoints } = await request.json()

    if (!purpose || !audience) {
      return NextResponse.json(
        { error: 'Purpose and audience are required' },
        { status: 400 }
      )
    }

    const gemini = new GeminiService()
    
    console.log('📧 Generating email with subject line...')
    const emailResult = await gemini.generateEmail(
      purpose,
      audience,
      tone || 'professional',
      keyPoints || [],
      true // Enable subject line generation
    )

    console.log('✅ Email with subject line generated successfully')

    // Use quota
    const quotaUsed = await QuotaManager.consumeQuota(session.user.id, 'emails')
    if (!quotaUsed) {
      console.error('Failed to update quota after successful generation')
    }

    // Parse the email result to extract subject and content
    let subject = '';
    let content = emailResult;
    
    // Check if the result contains a subject line
    const subjectMatch = emailResult.match(/^Subject:\s*(.+?)$/m);
    if (subjectMatch) {
      subject = subjectMatch[1].trim();
      content = emailResult.replace(/^Subject:\s*.+$/m, '').trim();
    } else {
      // Generate a subject line based on purpose if not included
      subject = `Re: ${purpose}`;
    }

    // Save content to database
    try {
      await prisma.content.create({
        data: {
          userId: session.user.id,
          type: 'email',
          title: subject || `Email: ${purpose}`,
          content: content,
          tone: tone || 'professional',
          metadata: JSON.stringify({
            purpose,
            audience,
            keyPoints,
            subject,
            generatedAt: new Date().toISOString()
          })
        }
      })
    } catch (dbError) {
      console.error('Failed to save content to database:', dbError)
      // Don't fail the request if saving fails
    }

    return NextResponse.json({
      success: true,
      content: content,
      subject: subject,
      quota: {
        used: quotaCheck.used + 1,
        limit: quotaCheck.limit,
        remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
      }
    })

  } catch (error) {
    console.error('Email generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate email' },
      { status: 500 }
    )
  }
}

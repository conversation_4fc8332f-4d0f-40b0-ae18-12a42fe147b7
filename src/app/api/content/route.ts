import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  let session: any = null
  try {
    // Check authentication
    session = await getServerSession(authOptions)
    console.log('Session check:', { 
      hasSession: !!session, 
      hasUser: !!session?.user, 
      hasUserId: !!session?.user?.id,
      userId: session?.user?.id 
    })
    
    if (!session?.user?.id) {
      console.log('Authentication failed - no session or user ID')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const id = searchParams.get('id')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    console.log('Content API request:', { type, id, limit, offset })

    // Build where clause
    const where: any = {
      userId: session.user.id
    }

    if (id) {
      where.id = id
    }

    if (type && type !== 'all') {
      where.type = type
    }

    // Fetch content with pagination and include image count
    console.log('Fetching content with params:', { where, limit, offset })
    
    const [content, totalCount] = await Promise.all([
      prisma.content.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        take: limit,
        skip: offset,
        select: {
          id: true,
          type: true,
          title: true,
          content: true,
          wordCount: true,
          tone: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          metadata: true,
          images: {
            select: {
              id: true,
              localPath: true,
              headingText: true,
              width: true,
              height: true
            }
          }
        }
      }),
      prisma.content.count({ where })
    ])
    
    console.log('Database query results:', { contentCount: content.length, totalCount })

    // Parse metadata for each content item with error handling
    const contentWithParsedMetadata = content.map((item: any) => {
      let parsedMetadata = null
      
      try {
        if (item.metadata) {
          parsedMetadata = JSON.parse(item.metadata)
        }
      } catch (error) {
        console.error('Failed to parse metadata for content item:', item.id, error)
        parsedMetadata = null
      }
      
      return {
        ...item,
        metadata: parsedMetadata,
        preview: item.content ? item.content.substring(0, 200) + (item.content.length > 200 ? '...' : '') : ''
      }
    })

    return NextResponse.json({
      success: true,
      content: contentWithParsedMetadata,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    })

  } catch (error) {
    console.error('Content fetch error:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      sessionUserId: session?.user?.id
    })
    return NextResponse.json(
      { error: 'Failed to fetch content', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { type, title, content, wordCount, tone, metadata } = body

    // Validate required fields
    if (!type || !title || !content) {
      return NextResponse.json(
        { error: 'Missing required fields: type, title, and content are required' },
        { status: 400 }
      )
    }

    // Ensure content is a string and calculate word count if not provided
    const contentString = typeof content === 'string' ? content :
                         typeof content === 'object' ? JSON.stringify(content) :
                         String(content)

    const calculatedWordCount = wordCount || contentString.split(/\s+/).filter(word => word.length > 0).length

    // Save content to database
    const savedContent = await prisma.content.create({
      data: {
        userId: session.user.id,
        type: type,
        title: title,
        content: contentString,
        wordCount: calculatedWordCount,
        tone: tone || 'professional',
        metadata: metadata ? JSON.stringify(metadata) : null,
        status: 'published'
      }
    })

    console.log('✅ Content saved to database with ID:', savedContent.id)

    return NextResponse.json({
      success: true,
      content: savedContent,
      message: 'Content saved successfully'
    })

  } catch (error) {
    console.error('Content save error:', error)
    return NextResponse.json(
      { error: 'Failed to save content', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const contentId = searchParams.get('id')
    const bulk = searchParams.get('bulk')

    // Handle bulk deletion
    if (bulk) {
      try {
        const body = await request.json()
        const { contentIds } = body

        if (!Array.isArray(contentIds) || contentIds.length === 0) {
          return NextResponse.json(
            { error: 'Content IDs array is required for bulk delete' },
            { status: 400 }
          )
        }

        // Verify ownership and delete
        const deletedContent = await prisma.content.deleteMany({
          where: {
            id: { in: contentIds },
            userId: session.user.id // Ensure user owns the content
          }
        })

        return NextResponse.json({
          success: true,
          message: `${deletedContent.count} content items deleted successfully`,
          deletedCount: deletedContent.count
        })
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid request body for bulk delete' },
          { status: 400 }
        )
      }
    }

    // Handle single deletion
    if (!contentId) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    // Verify ownership and delete
    const deletedContent = await prisma.content.deleteMany({
      where: {
        id: contentId,
        userId: session.user.id // Ensure user owns the content
      }
    })

    if (deletedContent.count === 0) {
      return NextResponse.json(
        { error: 'Content not found or unauthorized' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully'
    })

  } catch (error) {
    console.error('Content deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete content' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const contentId = searchParams.get('id')
    const action = searchParams.get('action')

    if (!contentId) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    // Handle different actions
    if (action === 'favorite') {
      const body = await request.json()
      const { isFavorite } = body

      // Update content metadata to mark as favorite
      const updatedContent = await prisma.content.updateMany({
        where: {
          id: contentId,
          userId: session.user.id
        },
        data: {
          metadata: JSON.stringify({
            ...JSON.parse('{}'),
            isFavorite: Boolean(isFavorite),
            updatedAt: new Date().toISOString()
          })
        }
      })

      if (updatedContent.count === 0) {
        return NextResponse.json(
          { error: 'Content not found or unauthorized' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        message: isFavorite ? 'Content marked as favorite' : 'Content unfavorited'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Content update error:', error)
    return NextResponse.json(
      { error: 'Failed to update content' },
      { status: 500 }
    )
  }
} 
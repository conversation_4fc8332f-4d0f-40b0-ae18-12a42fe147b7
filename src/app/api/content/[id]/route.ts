import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const { id } = resolvedParams

    if (!id) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    // Fetch the content from database
    const content = await prisma.content.findFirst({
      where: {
        id: id,
        userId: session.user.id // Ensure user can only access their own content
      }
    })

    if (!content) {
      return NextResponse.json(
        { error: 'Content not found' },
        { status: 404 }
      )
    }

    // Parse metadata if it exists
    let metadata = {}
    try {
      if (content.metadata) {
        metadata = JSON.parse(content.metadata)
      }
    } catch (error) {
      console.warn('Failed to parse content metadata:', error)
    }

    // Return the content
    return NextResponse.json({
      success: true,
      content: {
        id: content.id,
        title: content.title,
        content: content.content,
        type: content.type,
        wordCount: content.wordCount,
        tone: content.tone,
        language: content.language,
        status: content.status,
        createdAt: content.createdAt,
        updatedAt: content.updatedAt,
        metadata: {
          ...metadata,
          wordCount: content.wordCount,
          tone: content.tone,
          language: content.language,
          generatedAt: content.createdAt.toISOString()
        }
      }
    })

  } catch (error) {
    console.error('Content fetch error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch content' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const { id } = resolvedParams

    if (!id) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    // Delete the content (only if user owns it)
    const deletedContent = await prisma.content.deleteMany({
      where: {
        id: id,
        userId: session.user.id
      }
    })

    if (deletedContent.count === 0) {
      return NextResponse.json(
        { error: 'Content not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Content deleted successfully'
    })

  } catch (error) {
    console.error('Content deletion error:', error)
    return NextResponse.json(
      { error: 'Failed to delete content' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const { id } = resolvedParams

    if (!id) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    const { title, content, status } = await request.json()

    // Update the content (only if user owns it)
    const updatedContent = await prisma.content.updateMany({
      where: {
        id: id,
        userId: session.user.id
      },
      data: {
        ...(title && { title }),
        ...(content && { 
          content,
          wordCount: content.split(/\s+/).filter((word: string) => word.length > 0).length
        }),
        ...(status && { status }),
        updatedAt: new Date()
      }
    })

    if (updatedContent.count === 0) {
      return NextResponse.json(
        { error: 'Content not found or access denied' },
        { status: 404 }
      )
    }

    // Fetch and return the updated content
    const refreshedContent = await prisma.content.findFirst({
      where: {
        id: id,
        userId: session.user.id
      }
    })

    return NextResponse.json({
      success: true,
      content: refreshedContent
    })

  } catch (error) {
    console.error('Content update error:', error)
    return NextResponse.json(
      { error: 'Failed to update content' },
      { status: 500 }
    )
  }
}

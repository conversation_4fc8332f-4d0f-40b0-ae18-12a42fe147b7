'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>, 
  Brain, 
  Zap, 
  Target, 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  BarChart3,
  Users,
  Globe,
  Rocket,
  Star,
  ChevronRight,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react'

interface Agent {
  id: string
  name: string
  role: string
  status: 'idle' | 'working' | 'completed' | 'optimizing'
  progress: number
  task: string
  icon: React.ReactNode
  color: string
}

interface WorkflowStep {
  id: string
  name: string
  agent: string
  status: 'pending' | 'active' | 'completed'
  duration: number
  output?: string
}

interface GenerationMetrics {
  readability: number
  engagement: number
  seo: number
  viral: number
  overall: number
}

export default function AIContentSystemShowcase() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [generationComplete, setGenerationComplete] = useState(false)
  const [metrics, setMetrics] = useState<GenerationMetrics>({
    readability: 0,
    engagement: 0,
    seo: 0,
    viral: 0,
    overall: 0
  })

  const agents: Agent[] = [
    {
      id: 'researcher',
      name: 'Research Agent',
      role: 'Deep Web Research',
      status: 'idle',
      progress: 0,
      task: 'Gathering comprehensive data and insights',
      icon: <Globe className="w-5 h-5" />,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'analyst',
      name: 'Analysis Agent', 
      role: 'Pattern Recognition',
      status: 'idle',
      progress: 0,
      task: 'Extracting insights and identifying trends',
      icon: <BarChart3 className="w-5 h-5" />,
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'strategist',
      name: 'Strategy Agent',
      role: 'Viral Strategy',
      status: 'idle', 
      progress: 0,
      task: 'Developing viral content strategy',
      icon: <Target className="w-5 h-5" />,
      color: 'from-purple-500 to-violet-500'
    },
    {
      id: 'writer',
      name: 'Writing Agent',
      role: 'Content Creation',
      status: 'idle',
      progress: 0,
      task: 'Generating engaging content',
      icon: <Brain className="w-5 h-5" />,
      color: 'from-orange-500 to-red-500'
    },
    {
      id: 'critic',
      name: 'Critic Agent',
      role: 'Quality Review',
      status: 'idle',
      progress: 0,
      task: 'Critical analysis and improvements',
      icon: <CheckCircle className="w-5 h-5" />,
      color: 'from-yellow-500 to-amber-500'
    },
    {
      id: 'optimizer',
      name: 'Optimizer Agent',
      role: 'Performance Optimization',
      status: 'idle',
      progress: 0,
      task: 'Maximizing performance metrics',
      icon: <TrendingUp className="w-5 h-5" />,
      color: 'from-pink-500 to-rose-500'
    }
  ]

  const workflowSteps: WorkflowStep[] = [
    { id: '1', name: 'Research', agent: 'researcher', status: 'pending', duration: 15 },
    { id: '2', name: 'Analysis', agent: 'analyst', status: 'pending', duration: 12 },
    { id: '3', name: 'Strategy', agent: 'strategist', status: 'pending', duration: 18 },
    { id: '4', name: 'Writing', agent: 'writer', status: 'pending', duration: 25 },
    { id: '5', name: 'Critique', agent: 'critic', status: 'pending', duration: 10 },
    { id: '6', name: 'Optimize', agent: 'optimizer', status: 'pending', duration: 15 }
  ]

  const [steps, setSteps] = useState(workflowSteps)
  const [agentStates, setAgentStates] = useState(agents)

  const startGeneration = async () => {
    setIsGenerating(true)
    setGenerationComplete(false)
    setCurrentStep(0)
    
    // Reset states
    setSteps(workflowSteps.map(s => ({ ...s, status: 'pending' })))
    setAgentStates(agents.map(a => ({ ...a, status: 'idle', progress: 0 })))
    setMetrics({ readability: 0, engagement: 0, seo: 0, viral: 0, overall: 0 })

    // Simulate workflow execution
    for (let i = 0; i < workflowSteps.length; i++) {
      setCurrentStep(i)
      
      // Update step status
      setSteps(prev => prev.map((step, idx) => ({
        ...step,
        status: idx === i ? 'active' : idx < i ? 'completed' : 'pending'
      })))

      // Update agent status
      const currentAgent = workflowSteps[i].agent
      setAgentStates(prev => prev.map(agent => ({
        ...agent,
        status: agent.id === currentAgent ? 'working' : 
                agent.id === agents.find(a => a.id === workflowSteps[i-1]?.agent)?.id ? 'completed' : 
                agent.status,
        progress: agent.id === currentAgent ? 0 : agent.progress
      })))

      // Simulate progress
      for (let progress = 0; progress <= 100; progress += 5) {
        await new Promise(resolve => setTimeout(resolve, workflowSteps[i].duration * 10))
        setAgentStates(prev => prev.map(agent => ({
          ...agent,
          progress: agent.id === currentAgent ? progress : agent.progress
        })))
      }

      // Complete agent
      setAgentStates(prev => prev.map(agent => ({
        ...agent,
        status: agent.id === currentAgent ? 'completed' : agent.status
      })))

      // Update metrics gradually
      const stepMetrics = {
        readability: Math.min(95, (i + 1) * 15 + Math.random() * 5),
        engagement: Math.min(98, (i + 1) * 16 + Math.random() * 4),
        seo: Math.min(92, (i + 1) * 15 + Math.random() * 7),
        viral: Math.min(89, (i + 1) * 14 + Math.random() * 6),
        overall: 0
      }
      stepMetrics.overall = (stepMetrics.readability + stepMetrics.engagement + stepMetrics.seo + stepMetrics.viral) / 4
      setMetrics(stepMetrics)
    }

    // Complete all steps
    setSteps(prev => prev.map(step => ({ ...step, status: 'completed' })))
    setGenerationComplete(true)
    setIsGenerating(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-indigo-950 to-purple-950 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <Sparkles className="w-8 h-8 text-yellow-400" />
            <h1 className="text-6xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent">
              AI Content Studio 2.0
            </h1>
            <Sparkles className="w-8 h-8 text-yellow-400" />
          </div>
          <p className="text-2xl text-gray-300 mb-4">
            Powered by Kimi K2 Advanced Agent Architecture
          </p>
          <div className="flex items-center justify-center gap-6 text-gray-400">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-yellow-400" />
              <span>6 Specialized Agents</span>
            </div>
            <div className="flex items-center gap-2">
              <Rocket className="w-4 h-4 text-blue-400" />
              <span>Multi-Step Reasoning</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4 text-purple-400" />
              <span>World-Class Content</span>
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Agent Status Panel */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="lg:col-span-1"
          >
            <div className="bg-gray-900/50 border border-gray-700 rounded-2xl p-6 backdrop-blur-xl">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                <Brain className="w-5 h-5 text-purple-400" />
                Agent Network
              </h2>
              
              <div className="space-y-4">
                {agentStates.map((agent, index) => (
                  <motion.div
                    key={agent.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="relative"
                  >
                    <div className={`p-4 rounded-xl border transition-all duration-300 ${
                      agent.status === 'working' ? 'border-blue-400 bg-blue-500/10' :
                      agent.status === 'completed' ? 'border-green-400 bg-green-500/10' :
                      'border-gray-600 bg-gray-800/30'
                    }`}>
                      <div className="flex items-center gap-3 mb-2">
                        <div className={`p-2 rounded-lg bg-gradient-to-r ${agent.color}`}>
                          {agent.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-white text-sm">{agent.name}</h3>
                          <p className="text-xs text-gray-400">{agent.role}</p>
                        </div>
                        <div className={`w-2 h-2 rounded-full ${
                          agent.status === 'working' ? 'bg-blue-400 animate-pulse' :
                          agent.status === 'completed' ? 'bg-green-400' :
                          'bg-gray-500'
                        }`} />
                      </div>
                      
                      {agent.status === 'working' && (
                        <div className="mt-3">
                          <div className="flex justify-between text-xs text-gray-400 mb-1">
                            <span>{agent.task}</span>
                            <span>{agent.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-700 rounded-full h-1.5">
                            <motion.div
                              className="bg-blue-400 h-1.5 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ width: `${agent.progress}%` }}
                              transition={{ duration: 0.3 }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Generation Control */}
              <div className="mt-8 space-y-3">
                <button
                  onClick={startGeneration}
                  disabled={isGenerating}
                  className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      Start Demo
                    </>
                  )}
                </button>
                
                <a
                  href="/email-generator"
                  className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <Rocket className="w-4 h-4" />
                  Launch Email Generator
                </a>
              </div>
            </div>
          </motion.div>

          {/* Workflow & Content Panel */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2 space-y-6"
          >
            {/* Workflow Progress */}
            <div className="bg-gray-900/50 border border-gray-700 rounded-2xl p-6 backdrop-blur-xl">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                <Clock className="w-5 h-5 text-blue-400" />
                Workflow Progress
              </h2>
              
              <div className="flex items-center justify-between mb-4">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className={`w-10 h-10 rounded-full border-2 flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                      step.status === 'completed' ? 'border-green-400 bg-green-400 text-black' :
                      step.status === 'active' ? 'border-blue-400 bg-blue-400 text-black animate-pulse' :
                      'border-gray-600 text-gray-400'
                    }`}>
                      {step.status === 'completed' ? <CheckCircle className="w-5 h-5" /> : index + 1}
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-0.5 mx-2 transition-all duration-300 ${
                        steps[index + 1].status === 'completed' || steps[index + 1].status === 'active' 
                          ? 'bg-blue-400' : 'bg-gray-600'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                {steps.map((step) => (
                  <div key={step.id} className="p-3 rounded-lg bg-gray-800/50">
                    <p className={`font-semibold text-sm ${
                      step.status === 'completed' ? 'text-green-400' :
                      step.status === 'active' ? 'text-blue-400' :
                      'text-gray-400'
                    }`}>
                      {step.name}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">{step.duration}s</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Performance Metrics */}
            <div className="bg-gray-900/50 border border-gray-700 rounded-2xl p-6 backdrop-blur-xl">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-green-400" />
                Real-time Performance Metrics
              </h2>
              
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  {[
                    { name: 'Readability', value: metrics.readability, color: 'blue' },
                    { name: 'Engagement', value: metrics.engagement, color: 'green' },
                  ].map((metric) => (
                    <div key={metric.name}>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-300">{metric.name}</span>
                        <span className={`font-semibold ${
                          metric.color === 'blue' ? 'text-blue-400' :
                          metric.color === 'green' ? 'text-green-400' :
                          metric.color === 'purple' ? 'text-purple-400' :
                          'text-orange-400'
                        }`}>
                          {metric.value.toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full ${
                            metric.color === 'blue' ? 'bg-blue-400' :
                            metric.color === 'green' ? 'bg-green-400' :
                            metric.color === 'purple' ? 'bg-purple-400' :
                            'bg-orange-400'
                          }`}
                          initial={{ width: 0 }}
                          animate={{ width: `${metric.value}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="space-y-4">
                  {[
                    { name: 'SEO Score', value: metrics.seo, color: 'purple' },
                    { name: 'Viral Potential', value: metrics.viral, color: 'orange' },
                  ].map((metric) => (
                    <div key={metric.name}>
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-gray-300">{metric.name}</span>
                        <span className={`font-semibold ${
                          metric.color === 'blue' ? 'text-blue-400' :
                          metric.color === 'green' ? 'text-green-400' :
                          metric.color === 'purple' ? 'text-purple-400' :
                          'text-orange-400'
                        }`}>
                          {metric.value.toFixed(1)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div
                          className={`h-2 rounded-full ${
                            metric.color === 'blue' ? 'bg-blue-400' :
                            metric.color === 'green' ? 'bg-green-400' :
                            metric.color === 'purple' ? 'bg-purple-400' :
                            'bg-orange-400'
                          }`}
                          initial={{ width: 0 }}
                          animate={{ width: `${metric.value}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Overall Score */}
              <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-400/30">
                <div className="flex items-center justify-between">
                  <span className="text-lg font-semibold text-white">Overall Score</span>
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                      {metrics.overall.toFixed(1)}%
                    </span>
                    {metrics.overall > 85 && (
                      <Star className="w-5 h-5 text-yellow-400" />
                    )}
                  </div>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-3 mt-2">
                  <motion.div
                    className="bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${metrics.overall}%` }}
                    transition={{ duration: 0.8 }}
                  />
                </div>
              </div>
            </div>

            {/* Generation Complete */}
            <AnimatePresence>
              {generationComplete && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="bg-gradient-to-r from-green-900/50 to-emerald-900/50 border border-green-400/30 rounded-2xl p-6 backdrop-blur-xl"
                >
                  <div className="flex items-center gap-3 mb-4">
                    <CheckCircle className="w-6 h-6 text-green-400" />
                    <h3 className="text-xl font-bold text-white">Generation Complete!</h3>
                  </div>
                  <p className="text-gray-300 mb-4">
                    Your world-class content has been generated using advanced Kimi K2 agents. 
                    The content achieves an overall score of {metrics.overall.toFixed(1)}% with optimized 
                    readability, engagement, SEO, and viral potential.
                  </p>
                  <div className="flex gap-3">
                    <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                      View Content
                    </button>
                    <button className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                      Export Report
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="mt-12 grid md:grid-cols-3 gap-6"
        >
          {[
            {
              title: 'Autonomous Research',
              description: 'AI agents conduct comprehensive web research and fact-checking',
              icon: <Globe className="w-6 h-6" />,
              color: 'from-blue-500 to-cyan-500'
            },
            {
              title: 'Strategic Planning',
              description: 'Advanced algorithms develop viral content strategies',
              icon: <Target className="w-6 h-6" />,
              color: 'from-purple-500 to-violet-500'
            },
            {
              title: 'Quality Assurance',
              description: 'Multi-layer critique and optimization for perfect output',
              icon: <CheckCircle className="w-6 h-6" />,
              color: 'from-green-500 to-emerald-500'
            }
          ].map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
              className="bg-gray-900/30 border border-gray-700 rounded-xl p-6 backdrop-blur-sm hover:border-gray-600 transition-all duration-300"
            >
              <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-4`}>
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
              <p className="text-gray-400 text-sm">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  )
}

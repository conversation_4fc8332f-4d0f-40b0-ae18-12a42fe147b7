"use client";

import { useMemo } from "react";
import BlogDisplayLight from "@/components/BlogDisplayLight";

export default function BlogDisplayPage() {
  const params = useMemo(() => new URLSearchParams(typeof window !== 'undefined' ? window.location.search : ''), []);
  const title = params.get('title') || 'AI Generated Article';
  const article = params.get('article') || '';

  return <BlogDisplayLight title={title} markdown={article} />;
}



'use client'

import { useState, useEffect, use } from 'react'
import { motion } from 'framer-motion'
import {
  FileText,
  Download,
  Copy,
  Eye,
  ArrowLeft,
  Globe,
  Clock,
  Sparkles,
  ExternalLink
} from 'lucide-react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import StyleDisplay from '@/components/style'

interface GeneratedArticle {
  id: string
  title: string
  content: string
  metadata: {
    searchResults: number
    extractedSources: number
    sourceUrls: Array<{ title: string; url: string }>
    wordCount: number
    tone: string
    language: string
    generatedAt: string
    generationMethod?: string
  }
}

export default function ResultPage({ params }: { params: Promise<{ id: string }> }) {
  const { data: session } = useSession()
  const router = useRouter()
  const resolvedParams = use(params)
  const [article, setArticle] = useState<GeneratedArticle | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showPreview, setShowPreview] = useState(true)
  const [variant, setVariant] = useState<'default' | 'minimal' | 'magazine'>('default')
  const [showResearch, setShowResearch] = useState(false)

  useEffect(() => {
    if (!session) {
      router.push('/login')
      return
    }

    fetchArticle()
  }, [session, resolvedParams.id])

  const fetchArticle = async () => {
    try {
      const response = await fetch(`/api/content/${resolvedParams.id}`)
      const data = await response.json()

      if (data.success) {
        setArticle(data.content)
      } else {
        setError(data.error || 'Article not found')
      }
    } catch (error) {
      console.error('Fetch error:', error)
      setError('Failed to load article')
    } finally {
      setLoading(false)
    }
  }

  const handleCopy = async () => {
    if (article) {
      try {
        await navigator.clipboard.writeText(article.content)
      } catch (error) {
        console.error('Copy failed:', error)
      }
    }
  }

  const handleDownload = () => {
    if (article) {
      const element = document.createElement('a')
      const file = new Blob([article.content], { type: 'text/markdown' })
      element.href = URL.createObjectURL(file)
      element.download = `${article.title.replace(/[^a-z0-9]/gi, '_')}.md`
      document.body.appendChild(element)
      element.click()
      document.body.removeChild(element)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-400 mb-6">Please sign in to view this article.</p>
          <Link href="/login" className="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors">
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading article...</p>
        </div>
      </div>
    )
  }

  if (error || !article) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Article Not Found</h1>
          <p className="text-gray-400 mb-6">{error}</p>
          <Link href="/blog-generator" className="px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors">
            Back to Generator
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950">
      {/* Header */}
      <div className="border-b border-white/10 bg-gradient-to-r from-indigo-600/10 via-purple-600/10 to-fuchsia-600/10">
        <div className="container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link 
                href="/blog-generator"
                className="p-2 rounded-lg bg-white/10 hover:bg-white/20 text-gray-300 hover:text-white transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600">
                  <FileText className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white">Generated Article</h1>
                  <p className="text-indigo-200/80 text-sm">{article.metadata.wordCount} words • {article.metadata.tone}</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <select
                value={variant}
                onChange={(e) => setVariant(e.target.value as any)}
                className="bg-white/10 text-white/90 px-3 py-2 rounded-lg border border-white/10 focus:outline-none text-sm"
              >
                <option value="default" className="bg-slate-900">Default</option>
                <option value="minimal" className="bg-slate-900">Minimal</option>
                <option value="magazine" className="bg-slate-900">Magazine</option>
              </select>
              
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-gray-300 hover:text-white text-sm flex items-center gap-2"
              >
                <Eye className="w-4 h-4" /> {showPreview ? 'Markdown' : 'Preview'}
              </button>
              
              <button
                onClick={handleCopy}
                className="px-3 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-gray-300 hover:text-white text-sm flex items-center gap-2"
              >
                <Copy className="w-4 h-4" /> Copy
              </button>
              
              <button
                onClick={handleDownload}
                className="px-3 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-lg text-sm flex items-center gap-2"
              >
                <Download className="w-4 h-4" /> Download
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 xl:grid-cols-[1fr_300px] gap-6">
          {/* Article Display */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden">
            <div className="max-h-[80vh] overflow-y-auto">
              {showPreview ? (
                <StyleDisplay
                  title={article.title}
                  content={article.content}
                  metadata={article.metadata}
                  showMetadata={true}
                  variant={variant}
                />
              ) : (
                <div className="p-6">
                  <pre className="text-sm text-gray-300 whitespace-pre-wrap font-mono">
                    {article.content}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Article Info */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <h3 className="text-white font-semibold mb-4">Article Info</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-indigo-400" />
                  <span className="text-gray-300">{article.metadata.wordCount} words</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4 text-indigo-400" />
                  <span className="text-gray-300">{article.metadata.extractedSources || 0} sources</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-indigo-400" />
                  <span className="text-gray-300">{new Date(article.metadata.generatedAt).toLocaleDateString()}</span>
                </div>
                {article.metadata.generationMethod === 'openrouter_knowledge' && (
                  <div className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-amber-400" />
                    <span className="text-amber-300">AI Knowledge-Only</span>
                  </div>
                )}
              </div>
            </div>

            {/* Research Sources */}
            <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-semibold">Research Sources</h3>
                <button
                  onClick={() => setShowResearch(!showResearch)}
                  className="text-xs px-2 py-1 bg-white/10 hover:bg-white/20 rounded text-gray-300 hover:text-white"
                >
                  {showResearch ? 'Hide' : 'Show'}
                </button>
              </div>
              
              {showResearch && (
                <div className="space-y-2">
                  {(article.metadata.sourceUrls || []).slice(0, 10).map((source: any, index: number) => (
                    <a
                      key={index}
                      href={source.url}
                      target="_blank"
                      rel="noreferrer"
                      className="block p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group"
                    >
                      <div className="flex items-center gap-2">
                        <ExternalLink className="w-3 h-3 text-gray-400 group-hover:text-indigo-400" />
                        <span className="text-xs text-gray-300 group-hover:text-white truncate">
                          {source.title || source.url}
                        </span>
                      </div>
                    </a>
                  ))}
                  {!article.metadata.sourceUrls?.length && (
                    <p className="text-amber-300/80 text-sm">No web research included (knowledge-only mode)</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

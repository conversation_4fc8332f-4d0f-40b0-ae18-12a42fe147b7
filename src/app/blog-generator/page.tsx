'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { FileText, Search } from 'lucide-react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function BlogGeneratorPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [topic, setTopic] = useState('')
  const [wordCount, setWordCount] = useState(1500)
  const [tone, setTone] = useState('professional')
  const [language, setLanguage] = useState('en')
  const [error, setError] = useState('')

  const handleGenerate = async () => {
    if (!topic.trim()) {
      setError('Please enter a topic')
      return
    }

    // Navigate to a progress page with query params; that page will call the API and then redirect to the result
    const params = new URLSearchParams({
      topic,
      wordCount: String(wordCount),
      tone,
      language
    })
    router.push(`/blog-generator/progress?${params.toString()}`)
  }



  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-400 mb-6">Please sign in to use the blog generator.</p>
          <Link href="/login" className="px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
            Sign In
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950">
      <div className="container mx-auto px-6 py-24">
        <motion.div
          initial={{ opacity: 0, y: 12 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-3xl mx-auto"
        >
          <div className="text-center mb-6">
            <div className="inline-flex p-3 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg shadow-indigo-900/20">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <h1 className="mt-3 text-3xl font-bold text-white tracking-tight">Invincible .1v — Blog Post Studio</h1>
            <p className="text-indigo-200/80 text-sm md:text-base mt-1">Enter a topic and press Enter to generate. We’ll show progress and open your article when it’s ready.</p>
          </div>

          {/* Centered input card */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
            <div className="flex flex-col md:flex-row gap-3">
              <input
                type="text"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
                onKeyDown={(e) => { if (e.key === 'Enter') handleGenerate() }}
                placeholder="e.g., GPT-5 vs. GPT-4 — What changed?"
                className="flex-1 px-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-indigo-500/50 transition-colors"
              />
              <button
                onClick={handleGenerate}
                disabled={!topic.trim()}
                className="px-5 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                <Search className="w-4 h-4" /> Generate
              </button>
            </div>

            {/* Options row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-4">
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">Length</label>
                <select
                  value={wordCount}
                  onChange={(e) => setWordCount(Number(e.target.value))}
                  className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:border-indigo-500/50"
                >
                  <option value={800}>Short (≈800)</option>
                  <option value={1500}>Medium (≈1500)</option>
                  <option value={2500}>Long (≈2500)</option>
                  <option value={4000}>Comprehensive (≈4000)</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">Tone</label>
                <select
                  value={tone}
                  onChange={(e) => setTone(e.target.value)}
                  className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:border-indigo-500/50"
                >
                  <option value="professional">Professional</option>
                  <option value="casual">Casual</option>
                  <option value="friendly">Friendly</option>
                  <option value="authoritative">Authoritative</option>
                  <option value="conversational">Conversational</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-300 mb-1">Language</label>
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:border-indigo-500/50"
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="hi">Hindi</option>
                </select>
              </div>
            </div>

            {error && (
              <div className="mt-4 bg-red-500/10 border border-red-500/20 rounded-xl p-3 text-sm text-red-300">{error}</div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

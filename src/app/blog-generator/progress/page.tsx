'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Loader2, CheckCircle, AlertCircle, ArrowLeft, FileText, Search, Globe } from 'lucide-react'

export default function BlogProgressPage() {
  const params = useSearchParams()
  const router = useRouter()
  const [status, setStatus] = useState<'idle' | 'working' | 'done' | 'error'>('idle')
  const [message, setMessage] = useState('')
  const [progress, setProgress] = useState(0)
  const [steps, setSteps] = useState<string[]>([])

  const topic = params.get('topic') || ''

  useEffect(() => {
    const wordCount = Number(params.get('wordCount') || 1500)
    const tone = params.get('tone') || 'professional'
    const language = params.get('language') || 'en'

    if (!topic) {
      setStatus('error')
      setMessage('Missing topic')
      return
    }

    const run = async () => {
      try {
        setStatus('working')
        setMessage('Starting research and generation...')
        setProgress(10)
        setSteps(['Initializing...'])

        // Simulate progress steps
        setTimeout(() => {
          setMessage('Searching the web for latest information...')
          setProgress(30)
          setSteps(prev => [...prev, 'Web research in progress'])
        }, 500)

        setTimeout(() => {
          setMessage('Extracting and analyzing content...')
          setProgress(50)
          setSteps(prev => [...prev, 'Content extraction completed'])
        }, 1500)

        setTimeout(() => {
          setMessage('Generating article with AI...')
          setProgress(70)
          setSteps(prev => [...prev, 'AI writing in progress'])
        }, 2500)

        const res = await fetch('/api/generate/blog-post', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ topic, wordCount, tone, language })
        })

        const data = await res.json()
        if (data.success && data.article?.id) {
          setProgress(100)
          setSteps(prev => [...prev, 'Article generated successfully', 'Saved to content library'])
          setStatus('done')
          setMessage('Complete! Redirecting to your article...')

          // redirect to the result page
          setTimeout(() => {
            router.replace(`/blog-generator/result/${data.article.id}`)
          }, 1500)
        } else {
          setStatus('error')
          setMessage(data.error || 'Failed to generate article')
        }
      } catch (e: any) {
        setStatus('error')
        setMessage(e?.message || 'Unexpected error')
      }
    }

    run()
  }, [params, router, topic])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950">
      <div className="container mx-auto px-6 py-24">
        <motion.div
          initial={{ opacity: 0, y: 16 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-flex p-3 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg shadow-indigo-900/20 mb-4">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">Generating Your Article</h1>
            <p className="text-indigo-200/80 text-sm">Topic: <span className="font-medium">{topic}</span></p>
          </div>

          {/* Progress Card */}
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8">
            {/* Progress Bar */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-300">Progress</span>
                <span className="text-sm text-gray-300">{progress}%</span>
              </div>
              <div className="w-full bg-gray-700/50 rounded-full h-2">
                <motion.div
                  className="bg-gradient-to-r from-indigo-500 to-purple-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>

            {/* Current Step */}
            <div className="flex items-center gap-3 mb-6">
              {status === 'working' && <Loader2 className="w-5 h-5 animate-spin text-indigo-400" />}
              {status === 'done' && <CheckCircle className="w-5 h-5 text-green-400" />}
              {status === 'error' && <AlertCircle className="w-5 h-5 text-red-400" />}
              <span className="text-white font-medium">{message}</span>
            </div>

            {/* Steps List */}
            {steps.length > 0 && (
              <div className="space-y-2 mb-6">
                {steps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">{step}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Error Display */}
            {status === 'error' && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-6">
                <div className="flex items-center gap-3">
                  <AlertCircle className="w-5 h-5 text-red-400" />
                  <span className="text-red-300">{message}</span>
                </div>
              </div>
            )}

            {/* Success Message */}
            {status === 'done' && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-500/10 border border-green-500/20 rounded-xl p-4 mb-6"
              >
                <div className="flex items-center gap-3 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-green-300 font-medium">Article Generated Successfully!</span>
                </div>
                <p className="text-green-200/80 text-sm">Redirecting to article view...</p>
              </motion.div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3">
              <button
                onClick={() => router.push('/blog-generator')}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 text-gray-300 hover:text-white rounded-lg transition-colors text-sm flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" /> Back to Generator
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import StyleDisplay from '@/components/style'

interface BlogPost {
  id: string
  title: string
  content: string
  type: string
  metadata: any
  wordCount: number
  tone: string
  language: string
  createdAt: string
  updatedAt: string
}

export default function BlogViewPage() {
  const params = useParams()
  const [blogPost, setBlogPost] = useState<BlogPost | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [variant, setVariant] = useState<'default' | 'minimal' | 'magazine'>('default')

  useEffect(() => {
    if (params.id) {
      fetchBlogPost(params.id as string)
    }
  }, [params.id])

  const fetchBlogPost = async (id: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/content?id=${id}`)
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.content) {
          setBlogPost(data.content)
        } else {
          setError('Blog post not found')
        }
      } else {
        setError('Failed to load blog post')
      }
    } catch (error) {
      console.error('Error fetching blog post:', error)
      setError('Failed to load blog post')
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-violet-400 mx-auto mb-4" />
          <p className="text-gray-400">Loading blog post...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-white mb-2">Error</h1>
          <p className="text-gray-400 mb-6">{error}</p>
          <Link href="/content" className="px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
            Back to Content
          </Link>
        </div>
      </div>
    )
  }

  if (!blogPost) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-2">Blog Post Not Found</h1>
          <p className="text-gray-400 mb-6">The requested blog post could not be found.</p>
          <Link href="/content" className="px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors">
            Back to Content
          </Link>
        </div>
      </div>
    )
  }

  // Parse metadata if it's a string
  let metadata = blogPost.metadata
  if (typeof metadata === 'string') {
    try {
      metadata = JSON.parse(metadata)
    } catch (error) {
      console.error('Error parsing metadata:', error)
      metadata = {}
    }
  }

  return (
    <div className="relative">
      {/* Floating Controls */}
      <div className="fixed top-6 left-6 z-50 flex items-center space-x-4">
        <Link
          href="/content"
          className="p-3 bg-white/10 backdrop-blur-md hover:bg-white/20 rounded-xl text-white transition-colors border border-white/20"
        >
          <ArrowLeft className="w-5 h-5" />
        </Link>
        
        <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-2">
          <select
            value={variant}
            onChange={(e) => setVariant(e.target.value as any)}
            className="bg-transparent text-white text-sm focus:outline-none"
          >
            <option value="default" className="bg-gray-800">Default Style</option>
            <option value="minimal" className="bg-gray-800">Minimal Style</option>
            <option value="magazine" className="bg-gray-800">Magazine Style</option>
          </select>
        </div>
      </div>

      {/* Blog Post Display */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <StyleDisplay
          title={blogPost.title}
          content={blogPost.content}
          metadata={{
            ...metadata,
            generatedAt: blogPost.createdAt,
            wordCount: blogPost.wordCount,
            tone: blogPost.tone,
            language: blogPost.language
          }}
          showMetadata={true}
          variant={variant}
        />
      </motion.div>
    </div>
  )
}

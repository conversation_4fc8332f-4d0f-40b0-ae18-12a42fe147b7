'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import dynamic from 'next/dynamic'
import { 
  ArrowLeft,
  Save,
  Download,
  Share2,
  Eye,
  Code,
  Palette,
  Type,
  Image,
  Layout,
  Wand2,
  Sparkles,
  Copy,
  Check,
  Bold,
  Italic,
  Underline,
  AlignLeft,
  AlignCenter,
  AlignRight,
  List,
  ListOrdered,
  Quote,
  Link as LinkIcon,
  Heading1,
  Heading2,
  Heading3,
  Undo,
  Redo,
  Plus,
  X,
  ChevronRight,
  Sun,
  Moon,
  Zap,
  Feather
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Dynamically import the rich text editor
const RichTextEditor = dynamic(() => import('@/components/editor/RichTextEditor'), {
  ssr: false,
  loading: () => (
    <div className="animate-pulse">
      <div className="h-96 bg-white/5 rounded-2xl"></div>
    </div>
  )
})

// Theme interface
interface Theme {
  id: string
  name: string
  icon: any
  gradient: string
  bgPattern: string
  textColor: string
  accentColor: string
}

export default function ArticleEditorPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [content, setContent] = useState('')
  const [title, setTitle] = useState('Untitled Article')
  const [isPreview, setIsPreview] = useState(false)
  const [selectedTheme, setSelectedTheme] = useState('cosmic')
  const [showThemeSelector, setShowThemeSelector] = useState(false)
  const [saved, setSaved] = useState(false)
  const [copied, setCopied] = useState(false)
  const [wordCount, setWordCount] = useState(0)
  const [readingTime, setReadingTime] = useState(0)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-300">Loading editor...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  // Load content from URL params or localStorage
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const passedContent = urlParams.get('content')
    const passedTitle = urlParams.get('title')
    
    if (passedContent) {
      setContent(decodeURIComponent(passedContent))
    } else {
      // Load from localStorage
      const savedContent = localStorage.getItem('articleContent')
      if (savedContent) setContent(savedContent)
    }
    
    if (passedTitle) {
      setTitle(decodeURIComponent(passedTitle))
    }
  }, [])

  // Calculate word count and reading time
  useEffect(() => {
    const text = content.replace(/<[^>]*>/g, '')
    const words = text.trim().split(/\s+/).length
    setWordCount(words)
    setReadingTime(Math.ceil(words / 200)) // 200 words per minute
  }, [content])

  // Auto-save to localStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      localStorage.setItem('articleContent', content)
      setSaved(true)
      setTimeout(() => setSaved(false), 2000)
    }, 1000)

    return () => clearTimeout(timer)
  }, [content])

  // Themes configuration
  const themes: Theme[] = [
    {
      id: 'cosmic',
      name: 'Cosmic',
      icon: Sparkles,
      gradient: 'from-violet-900 via-purple-900 to-indigo-900',
      bgPattern: 'bg-gradient-to-br',
      textColor: 'text-white',
      accentColor: 'violet'
    },
    {
      id: 'aurora',
      name: 'Aurora',
      icon: Sun,
      gradient: 'from-green-900 via-emerald-900 to-teal-900',
      bgPattern: 'bg-gradient-to-tr',
      textColor: 'text-white',
      accentColor: 'emerald'
    },
    {
      id: 'sunset',
      name: 'Sunset',
      icon: Moon,
      gradient: 'from-orange-900 via-red-900 to-pink-900',
      bgPattern: 'bg-gradient-to-bl',
      textColor: 'text-white',
      accentColor: 'orange'
    },
    {
      id: 'ocean',
      name: 'Ocean',
      icon: Zap,
      gradient: 'from-blue-900 via-cyan-900 to-teal-900',
      bgPattern: 'bg-gradient-to-tl',
      textColor: 'text-white',
      accentColor: 'blue'
    }
  ]

  const activeTheme = themes.find(t => t.id === selectedTheme) || themes[0]

  const handleSave = () => {
    // Save to database or export
    localStorage.setItem('articleContent', content)
    localStorage.setItem('articleTitle', title)
    setSaved(true)
    setTimeout(() => setSaved(false), 2000)
  }

  const handleExport = () => {
    const blob = new Blob([content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title.replace(/\s+/g, '-')}.html`
    a.click()
    URL.revokeObjectURL(url)
  }

  const handleCopy = async () => {
    await navigator.clipboard.writeText(content)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div className={cn(
      "min-h-screen transition-all duration-500",
      activeTheme.bgPattern,
      activeTheme.gradient
    )}>
      {/* Animated Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -top-1/4 -right-1/4 w-[800px] h-[800px] bg-white/5 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -bottom-1/4 -left-1/4 w-[600px] h-[600px] bg-white/5 rounded-full blur-3xl"
        />
      </div>

      {/* Top Navigation */}
      <nav className="relative z-20 backdrop-blur-xl bg-black/20 border-b border-white/10">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link href="/dashboard" className="flex items-center space-x-2 text-white/70 hover:text-white transition-colors">
                <ArrowLeft className="w-5 h-5" />
                <span>Back to Dashboard</span>
              </Link>
              
              <div className="flex items-center space-x-3">
                <Feather className="w-6 h-6 text-white" />
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="text-2xl font-bold bg-transparent text-white placeholder-white/50 focus:outline-none"
                  placeholder="Enter article title..."
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Theme Selector */}
              <div className="relative">
                <button
                  onClick={() => setShowThemeSelector(!showThemeSelector)}
                  className="p-2.5 text-white/70 hover:text-white bg-white/10 rounded-lg transition-all"
                >
                  <Palette className="w-5 h-5" />
                </button>

                <AnimatePresence>
                  {showThemeSelector && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      className="absolute right-0 mt-2 p-2 bg-black/90 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl"
                    >
                      {themes.map((theme) => (
                        <button
                          key={theme.id}
                          onClick={() => {
                            setSelectedTheme(theme.id)
                            setShowThemeSelector(false)
                          }}
                          className={cn(
                            "w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all",
                            selectedTheme === theme.id
                              ? "bg-white/20 text-white"
                              : "text-white/70 hover:text-white hover:bg-white/10"
                          )}
                        >
                          <theme.icon className="w-4 h-4" />
                          <span className="text-sm font-medium">{theme.name}</span>
                        </button>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Preview Toggle */}
              <button
                onClick={() => setIsPreview(!isPreview)}
                className={cn(
                  "p-2.5 rounded-lg transition-all",
                  isPreview
                    ? "bg-white/20 text-white"
                    : "text-white/70 hover:text-white bg-white/10"
                )}
              >
                {isPreview ? <Code className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>

              {/* Action Buttons */}
              <button
                onClick={handleCopy}
                className="p-2.5 text-white/70 hover:text-white bg-white/10 rounded-lg transition-all"
              >
                {copied ? <Check className="w-5 h-5" /> : <Copy className="w-5 h-5" />}
              </button>

              <button
                onClick={handleExport}
                className="p-2.5 text-white/70 hover:text-white bg-white/10 rounded-lg transition-all"
              >
                <Download className="w-5 h-5" />
              </button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleSave}
                className="px-6 py-2.5 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-lg font-medium shadow-lg flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>{saved ? 'Saved!' : 'Save'}</span>
              </motion.button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Editor Area */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Editor */}
          <div className="lg:col-span-3">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl overflow-hidden shadow-2xl"
            >
              {isPreview ? (
                <div className="p-8">
                  <article 
                    className="prose prose-invert prose-lg max-w-none"
                    dangerouslySetInnerHTML={{ __html: content }}
                  />
                </div>
              ) : (
                <RichTextEditor
                  content={content}
                  onChange={setContent}
                  placeholder="Start writing your masterpiece..."
                  className="min-h-[600px]"
                />
              )}
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Layout className="w-5 h-5" />
                Article Stats
              </h3>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-white/70">Word Count</p>
                  <p className="text-2xl font-bold text-white">{wordCount.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-white/70">Reading Time</p>
                  <p className="text-2xl font-bold text-white">{readingTime} min</p>
                </div>
                <div>
                  <p className="text-sm text-white/70">Status</p>
                  <p className="text-sm font-medium text-emerald-400">Auto-saving...</p>
                </div>
              </div>
            </motion.div>

            {/* AI Tools */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Wand2 className="w-5 h-5" />
                AI Assistant
              </h3>
              
              <div className="space-y-3">
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all flex items-center justify-between">
                  <span>Improve Writing</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all flex items-center justify-between">
                  <span>Generate Ideas</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all flex items-center justify-between">
                  <span>Add Citations</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all flex items-center justify-between">
                  <span>SEO Optimize</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </motion.div>

            {/* Export Options */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-2xl p-6"
            >
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                <Share2 className="w-5 h-5" />
                Export & Share
              </h3>
              
              <div className="space-y-3">
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all">
                  Export as PDF
                </button>
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all">
                  Export as Markdown
                </button>
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all">
                  Publish Article
                </button>
                <button className="w-full px-4 py-3 bg-white/10 hover:bg-white/20 rounded-xl text-white text-sm font-medium transition-all">
                  Share Link
                </button>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 
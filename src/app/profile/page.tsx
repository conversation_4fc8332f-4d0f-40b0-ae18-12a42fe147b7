'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  User,
  Mail,
  Calendar,
  Crown,
  BarChart3,
  TrendingUp,
  Clock,
  Settings,
  Home,
  Edit3,
  Camera,
  Shield,
  Award,
  Activity,
  FileText,
  Zap
} from 'lucide-react'

interface UserProfile {
  id: string
  name: string | null
  email: string | null
  image: string | null
  firstName: string | null
  lastName: string | null
  bio: string | null
  createdAt: string
  subscription?: {
    plan: string
    status: string
  }
  stats?: {
    totalContent: number
    totalUsage: number
  }
}

export default function ProfilePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Fetch user profile data
  useEffect(() => {
    if (session?.user) {
      fetchUserProfile()
    }
  }, [session])

  const fetchUserProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        const data = await response.json()
        setUserProfile(data)
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Loading state
  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading your profile...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  if (!userProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-gray-400">Unable to load profile</p>
          <Link href="/dashboard">
            <button className="px-4 py-2 bg-violet-600 text-white rounded-lg">
              Back to Dashboard
            </button>
          </Link>
        </div>
      </div>
    )
  }

  const getUserInitials = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName[0]}${userProfile.lastName[0]}`
    } else if (userProfile?.name) {
      const names = userProfile.name.split(' ')
      return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0]
    } else if (userProfile?.email) {
      return userProfile.email[0].toUpperCase()
    }
    return 'U'
  }

  const getDisplayName = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName} ${userProfile.lastName}`
    } else if (userProfile?.name) {
      return userProfile.name
    } else if (userProfile?.email) {
      return userProfile.email.split('@')[0]
    }
    return 'User'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <div className="border-b border-white/10 bg-black/20 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link href="/dashboard">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  className="flex items-center space-x-3 text-white hover:text-violet-400 transition-colors"
                >
                  <Home className="w-5 h-5" />
                  <span className="font-semibold">Dashboard</span>
                </motion.button>
              </Link>
              
              <div className="text-gray-600">|</div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent">
                My Profile
              </h1>
            </div>
            
            <Link href="/settings">
              <motion.button
                whileHover={{ scale: 1.05 }}
                className="flex items-center space-x-2 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-xl transition-colors"
              >
                <Settings className="w-4 h-4" />
                <span>Edit Profile</span>
              </motion.button>
            </Link>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 text-center"
            >
              {/* Avatar */}
              <div className="relative inline-block mb-6">
                {userProfile.image ? (
                  <img
                    src={userProfile.image}
                    alt={getDisplayName()}
                    className="w-32 h-32 rounded-3xl object-cover border-4 border-white/20"
                  />
                ) : (
                  <div className="w-32 h-32 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-3xl flex items-center justify-center text-white text-4xl font-bold">
                    {getUserInitials()}
                  </div>
                )}
                <button className="absolute -bottom-2 -right-2 w-10 h-10 bg-violet-600 hover:bg-violet-700 rounded-full flex items-center justify-center text-white transition-colors">
                  <Camera className="w-5 h-5" />
                </button>
              </div>

              {/* User Info */}
              <h2 className="text-2xl font-bold text-white mb-2">
                {getDisplayName()}
              </h2>
              <p className="text-gray-400 mb-4">{userProfile.email}</p>
              
              {/* Subscription Badge */}
              <div className="flex items-center justify-center space-x-2 mb-6">
                <Crown className={`w-5 h-5 ${userProfile.subscription?.plan === 'free' ? 'text-gray-400' : 'text-yellow-400'}`} />
                <span className={`font-medium ${userProfile.subscription?.plan === 'free' ? 'text-gray-400' : 'text-yellow-400'}`}>
                  {userProfile.subscription?.plan === 'free' ? 'Free Plan' : 
                   userProfile.subscription?.plan === 'pro' ? 'Pro Member' : 'Member'}
                </span>
              </div>

              {/* Bio */}
              {userProfile.bio && (
                <p className="text-gray-300 text-sm leading-relaxed mb-6">
                  {userProfile.bio}
                </p>
              )}

              {/* Join Date */}
              <div className="flex items-center justify-center space-x-2 text-gray-400 text-sm">
                <Calendar className="w-4 h-4" />
                <span>Joined {formatDate(userProfile.createdAt)}</span>
              </div>
            </motion.div>
          </div>

          {/* Stats and Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Stats Grid */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-6"
            >
              <div className="bg-gradient-to-br from-blue-500/20 to-blue-600/10 backdrop-blur-xl border border-blue-500/20 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <BarChart3 className="w-8 h-8 text-blue-400" />
                  <TrendingUp className="w-5 h-5 text-emerald-400" />
                </div>
                <p className="text-3xl font-bold text-white mb-1">{userProfile.stats?.totalContent || 0}</p>
                <p className="text-sm text-gray-400">Content Created</p>
              </div>

              <div className="bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 backdrop-blur-xl border border-emerald-500/20 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <Award className="w-8 h-8 text-emerald-400" />
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <div
                        key={star}
                        className="w-3 h-3 rounded-full bg-yellow-400"
                      />
                    ))}
                  </div>
                </div>
                <p className="text-3xl font-bold text-white mb-1">9.8/10</p>
                <p className="text-sm text-gray-400">Quality Score</p>
              </div>

              <div className="bg-gradient-to-br from-purple-500/20 to-purple-600/10 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <Clock className="w-8 h-8 text-purple-400" />
                  <Activity className="w-5 h-5 text-blue-400" />
                </div>
                <p className="text-3xl font-bold text-white mb-1">48h</p>
                <p className="text-sm text-gray-400">Time Saved</p>
              </div>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8"
            >
              <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                <Zap className="w-6 h-6 mr-3 text-violet-400" />
                Quick Actions
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href="/settings">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    className="w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left"
                  >
                    <Settings className="w-5 h-5 text-violet-400" />
                    <div>
                      <p className="font-medium text-white">Account Settings</p>
                      <p className="text-sm text-gray-400">Manage preferences</p>
                    </div>
                  </motion.button>
                </Link>

                <Link href="/dashboard">
                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    className="w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left"
                  >
                    <Home className="w-5 h-5 text-blue-400" />
                    <div>
                      <p className="font-medium text-white">Dashboard</p>
                      <p className="text-sm text-gray-400">Back to main hub</p>
                    </div>
                  </motion.button>
                </Link>



              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
} 
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  firstName     String?
  lastName      String?
  bio           String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Authentication
  accounts Account[]
  sessions Session[]

  // User Settings
  settings UserSettings?

  // Subscription & Quotas
  subscription Subscription?
  quotas       UserQuota[]

  // Content & Usage
  content            Content[]
  usageHistory       UsageHistory[]
  apiKeys            ApiKey[]
  knowledgeBase      KnowledgeBase[]
  competitorAnalysis CompetitorAnalysis[]

  @@map("users")
}

model UserSettings {
  id                        String  @id @default(cuid())
  userId                    String  @unique

  // Content Preferences
  defaultLanguage           String  @default("en")
  timezone                  String  @default("America/New_York")
  defaultWordCount          Int     @default(1000)
  defaultTone               String  @default("professional")
  includeResearchByDefault  Boolean @default(true)
  autoSaveEnabled           Boolean @default(true)

  // Notifications
  emailNotifications        Boolean @default(true)
  pushNotifications         Boolean @default(false)
  weeklyReports             Boolean @default(true)
  marketingEmails           Boolean @default(false)

  // Appearance
  theme                     String  @default("dark")
  accentColor               String  @default("blue")
  animationsEnabled         Boolean @default(true)
  compactMode               Boolean @default(false)

  // Privacy
  profileVisibility         String  @default("private")
  dataSharing               Boolean @default(false)
  analyticsTracking         Boolean @default(true)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}

model Subscription {
  id        String   @id @default(cuid())
  userId    String   @unique
  plan      String   // free, pro, enterprise
  status    String   // active, canceled, expired
  startDate DateTime @default(now())
  endDate   DateTime?

  // Stripe/Payment info
  stripeCustomerId       String?
  stripeSubscriptionId   String?
  stripePriceId          String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("subscriptions")
}

model UserQuota {
  id               String   @id @default(cuid())
  userId           String
  quotaType        String   // blog_posts, emails, social_media, youtube_scripts
  totalLimit       Int      // Monthly limit
  used             Int      @default(0)
  resetDate        DateTime // When quota resets (usually monthly)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, quotaType])
  @@map("user_quotas")
}

model Content {
  id          String   @id @default(cuid())
  userId      String
  type        String   // blog, email, social_media, youtube_script
  title       String
  content     String
  metadata    String?  // JSON string with additional data
  metaDescription String?

  status      String   @default("draft") // draft, published, archived
  wordCount   Int?
  tone        String?
  language    String   @default("en")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  images StoredImage[] // Associated images

  @@map("content")
}

// Stored Images for blog posts
model StoredImage {
  id          String   @id @default(cuid())
  originalUrl String   // Original Ideogram URL
  localPath   String   // Web-accessible path (/images/blog/webp/filename.webp)
  filename    String   // WebP filename
  size        Int      // File size in bytes
  width       Int      // Image width
  height      Int      // Image height
  contentId   String?  // Associated content ID (optional)
  headingText String?  // Heading text this image was generated for
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Optional relation to content
  content Content? @relation(fields: [contentId], references: [id], onDelete: SetNull)

  @@map("stored_images")
}

model UsageHistory {
  id          String   @id @default(cuid())
  userId      String
  action      String   // content_generated, api_call, feature_used
  type        String   // blog, email, social_media, youtube_script
  metadata    String?  // JSON string with additional data
  tokensUsed  Int?     // For AI API usage tracking
  costInCents Int?     // Cost tracking
  timestamp   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("usage_history")
}

model ApiKey {
  id        String   @id @default(cuid())
  userId    String
  name      String
  key       String   @unique
  isActive  Boolean  @default(true)
  lastUsed  DateTime?
  createdAt DateTime @default(now())
  expiresAt DateTime?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model ContentAnalysis {
  id                     String   @id @default(cuid())
  taskId                 String   @unique
  detectedNiche          String
  articleType            String
  competitorAnalysis     String   // JSON string
  contentGaps            String   // JSON array
  informationRequirements String  // JSON array
  dataNeeds              String   // JSON array
  researchAreas          String   // JSON array
  uniqueValueProposition String
  recommendedSearchQueries String // JSON array
  analysisMetadata       String?  // JSON string
  createdAt              DateTime @default(now())

  @@map("content_analysis")
}

model WritingAnalysis {
  id                     String   @id @default(cuid())
  taskId                 String   @unique
  writingStyle           String   // JSON string
  contentFlow            String   // JSON string
  headingStructure       String   // JSON string
  engagementTechniques   String   // JSON array
  competitiveAnalysis    String   // JSON array
  humanWritingElements   String   // JSON array
  contentConveyance      String   // JSON string
  improvementOpportunities String // JSON array
  analysisMetadata       String?  // JSON string
  createdAt              DateTime @default(now())

  @@map("writing_analysis")
}

// Knowledge Base for storing researched content
model KnowledgeBase {
  id              String   @id @default(cuid())
  userId          String
  topic           String
  sourceUrl       String
  title           String
  content         String   // Main extracted content

  // SEO and metadata
  metaDescription String?
  metaKeywords    String?  // JSON array
  headingStructure String? // JSON object with H1-H6 structure

  // Content analysis
  wordCount       Int?
  keywordsFound   String?  // JSON array of found keywords
  internalLinks   String?  // JSON array of internal links
  externalLinks   String?  // JSON array of external links

  // Quality metrics
  readabilityScore Float?
  contentScore     Float?

  // Metadata
  extractedAt     DateTime @default(now())
  lastUpdated     DateTime @updatedAt
  isActive        Boolean  @default(true)

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  seoInsights     SEOInsights[]

  @@index([userId, topic])
  @@index([sourceUrl])
  @@map("knowledge_base")
}

// Competitive Analysis results
model CompetitorAnalysis {
  id                String   @id @default(cuid())
  userId            String
  topic             String
  competitorUrl     String
  competitorTitle   String?

  // SEO Analysis
  titleAnalysis     String?  // JSON object with title insights
  metaAnalysis      String?  // JSON object with meta tag analysis
  headingAnalysis   String?  // JSON object with heading structure
  keywordAnalysis   String?  // JSON object with keyword usage

  // Content Structure
  contentLength     Int?
  contentStructure  String?  // JSON object with content organization
  contentGaps       String?  // JSON array of identified gaps
  contentStrengths  String?  // JSON array of competitor strengths

  // Opportunities
  improvementAreas  String?  // JSON array of areas we can improve
  uniqueAngles      String?  // JSON array of suggested unique approaches
  recommendations   String?  // JSON object with specific recommendations

  // Scoring
  overallScore      Float?
  seoScore          Float?
  contentScore      Float?

  // Metadata
  analyzedAt        DateTime @default(now())
  lastUpdated       DateTime @updatedAt
  isActive          Boolean  @default(true)

  // Relations
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, topic])
  @@index([competitorUrl])
  @@map("competitor_analysis")
}

// SEO Insights extracted from content
model SEOInsights {
  id                 String        @id @default(cuid())
  knowledgeBaseId    String

  // Keyword Analysis
  primaryKeywords    String?       // JSON array
  secondaryKeywords  String?       // JSON array
  longTailKeywords   String?       // JSON array
  keywordDensity     String?       // JSON object with density data

  // Content Structure
  headingStructure   String?       // JSON object (H1, H2, H3, etc.)
  contentOutline     String?       // JSON array of main sections

  // Technical SEO
  metaTitle          String?
  metaDescription    String?
  canonicalUrl       String?
  schemaMarkup       String?       // JSON object

  // Performance Indicators
  estimatedReadTime  Int?          // in minutes
  contentFreshness   String?       // publication/update date
  authorityScore     Float?        // estimated domain authority

  // Recommendations
  seoRecommendations String?       // JSON array of improvement suggestions
  contentSuggestions String?       // JSON array of content improvements

  // Metadata
  extractedAt        DateTime      @default(now())
  lastUpdated        DateTime      @updatedAt

  // Relations
  knowledgeBase      KnowledgeBase @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)

  @@index([knowledgeBaseId])
  @@map("seo_insights")
}
